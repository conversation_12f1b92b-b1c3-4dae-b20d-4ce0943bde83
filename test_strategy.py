# test_strategy.py
# Quick test for the advanced multi-indicator strategy

from datetime import datetime, timedelta
from simulation import AdvancedTradingSimulation


def quick_advanced_test():
    """
    Quick test of advanced strategy on last 3 days
    """
    print("⚡ Advanced Strategy Quick Test - Last 3 Days")
    print("="*60)
    
    # Define period
    end_date = datetime.now()
    start_date = end_date - timedelta(days=3)
    
    print(f"📅 Period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    
    # Create advanced simulator
    sim = AdvancedTradingSimulation(
        initial_balance=1000,   # $1,000 for quick test
        base_lot_size=0.01,     # Base 0.01 lot (will be adjusted by confidence)
        spread=0.5              # 0.5 point spread
    )
    
    # Run simulation
    success = sim.run_advanced_simulation(start_date, end_date, candles_per_step=50)
    
    if success:
        results = sim.print_advanced_results()
        
        # Advanced analysis
        print("\n🔍 Advanced Analysis:")
        if results['total_trades'] > 0:
            if results['profit_percentage'] > 0:
                print("✅ Strategy is profitable")
            else:
                print("⚠️ Strategy needs improvement")
            
            if results['win_rate'] > 50:
                print(f"✅ Good win rate: {results['win_rate']:.1f}%")
            else:
                print(f"⚠️ Low win rate: {results['win_rate']:.1f}%")
            
            if results['avg_confidence'] > 70:
                print(f"✅ High average confidence: {results['avg_confidence']:.1f}%")
            else:
                print(f"⚠️ Low average confidence: {results['avg_confidence']:.1f}%")
            
            if results['max_drawdown'] < 10:
                print(f"✅ Low drawdown: {results['max_drawdown']:.1f}%")
            else:
                print(f"⚠️ High drawdown: {results['max_drawdown']:.1f}%")
        else:
            print("ℹ️ No trades executed - strategy is very conservative")
        
        return results
    else:
        print("❌ Test failed")
        return None


def weekly_advanced_test():
    """
    Test advanced strategy on last week
    """
    print("📅 Advanced Strategy Weekly Test - Last 7 Days")
    print("="*60)
    
    # Define period
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    
    print(f"📅 Period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    
    # Create simulator with larger balance for weekly test
    sim = AdvancedTradingSimulation(
        initial_balance=5000,   # $5,000 for weekly test
        base_lot_size=0.01,     # Base lot size
        spread=0.5              # Spread
    )
    
    # Run simulation
    success = sim.run_advanced_simulation(start_date, end_date, candles_per_step=100)
    
    if success:
        results = sim.print_advanced_results()
        return results
    else:
        print("❌ Weekly test failed")
        return None


def compare_strategies():
    """
    Compare advanced strategy with simple strategy
    """
    print("🔬 Strategy Comparison Test")
    print("="*60)
    
    end_date = datetime.now()
    start_date = end_date - timedelta(days=5)
    
    print(f"📅 Comparison period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    
    # Test advanced strategy
    print("\n🤖 Testing Advanced Strategy...")
    advanced_sim = AdvancedTradingSimulation(
        initial_balance=2000,
        base_lot_size=0.01,
        spread=0.5
    )
    
    advanced_success = advanced_sim.run_advanced_simulation(start_date, end_date, candles_per_step=75)
    
    if advanced_success:
        advanced_results = advanced_sim.print_advanced_results()
        
        print("\n📊 Strategy Comparison Summary:")
        print("-" * 40)
        print(f"Advanced Strategy:")
        print(f"  💰 Profit: ${advanced_results['total_profit']:+.2f} ({advanced_results['profit_percentage']:+.2f}%)")
        print(f"  📊 Trades: {advanced_results['total_trades']}")
        print(f"  🎯 Win Rate: {advanced_results['win_rate']:.1f}%")
        print(f"  🔥 Avg Confidence: {advanced_results['avg_confidence']:.1f}%")
        print(f"  📉 Max Drawdown: {advanced_results['max_drawdown']:.1f}%")
        
        return advanced_results
    else:
        print("❌ Comparison test failed")
        return None


def performance_benchmark():
    """
    Performance benchmark test
    """
    print("⏱️ Advanced Strategy Performance Benchmark")
    print("="*60)
    
    start_time = datetime.now()
    
    # Run quick test
    results = quick_advanced_test()
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    print(f"\n⏱️ Execution time: {duration:.2f} seconds")
    
    if duration < 30:
        print("✅ Excellent performance - very fast")
    elif duration < 60:
        print("✅ Good performance")
    else:
        print("⚠️ Slow performance - consider optimization")
    
    return results, duration


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        test_type = sys.argv[1].lower()
        
        if test_type == "quick":
            quick_advanced_test()
        elif test_type == "weekly":
            weekly_advanced_test()
        elif test_type == "compare":
            compare_strategies()
        elif test_type == "benchmark":
            performance_benchmark()
        else:
            print("Available tests: quick, weekly, compare, benchmark")
    else:
        # Default: run quick test
        print("🚀 Running default quick test...")
        results, duration = performance_benchmark()
        
        if results:
            print(f"\n🎯 Quick Summary:")
            print(f"   💰 Profit: ${results['total_profit']:+.2f}")
            print(f"   📊 Trades: {results['total_trades']}")
            print(f"   ⏱️ Time: {duration:.1f}s")
            
            if results['total_trades'] > 0:
                print(f"   🎯 Success: {results['win_rate']:.1f}%")
                print(f"   🔥 Confidence: {results['avg_confidence']:.1f}%")
        
        print("\n✅ Advanced strategy test completed!")
        print("💡 For more tests, use: python test_strategy.py [quick|weekly|compare|benchmark]")
