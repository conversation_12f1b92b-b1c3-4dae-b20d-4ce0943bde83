#!/usr/bin/env python3
# test_dual_confirmation.py
# Test dual confirmation system (minimum 2 rules required)

import sys
from datetime import datetime, timedelta
from simulation import AdvancedTradingSimulation

def test_dual_confirmation_system():
    """
    Test dual confirmation system requiring minimum 2 rules
    """
    print("🔥 Testing DUAL CONFIRMATION System")
    print("=" * 60)
    print("📋 New approach:")
    print("   • Minimum 2 rules must agree for trade")
    print("   • Higher confidence through confirmation")
    print("   • Quality over quantity approach")
    print("   • Better risk management")
    print("=" * 60)
    
    # Create simulation instance
    sim = AdvancedTradingSimulation(
        initial_balance=10000,
        base_lot_size=0.02,
        spread=0.5,
        use_stop_loss=True
    )
    
    # Balanced parameters
    sim.max_positions = 3
    sim.stop_loss_percentage = 0.005
    
    # Test period (last 3 days)
    end_date = datetime.now()
    start_date = end_date - timedelta(days=3)
    
    print(f"📅 Test period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    print(f"🎯 Max positions: {sim.max_positions}")
    print(f"🛡️ Stop loss: {sim.stop_loss_percentage*100:.1f}%")
    print(f"📊 Min rules required: 2")
    print()
    
    # Run the simulation with dual confirmation (already implemented in simulation.py)
    print(f"🚀 Starting Dual Confirmation Test")
    print("-" * 60)

    success = sim.run_individual_signals_simulation(start_date, end_date)
        

    
    if success:
        print("\n" + "🎉 Dual Confirmation Test Completed!")
        print("=" * 60)
        
        # Print enhanced results
        results = sim.print_advanced_results()
        
        # Additional dual confirmation analysis
        print("\n📊 Dual Confirmation Analysis:")
        print("-" * 40)
        
        if hasattr(sim, 'dual_confirmations') and sim.dual_confirmations:
            print(f"🔄 Total confirmations: {len(sim.dual_confirmations)}")
            
            # Analyze confirmation patterns
            buy_confirmations = [c for c in sim.dual_confirmations if c['type'] == 'BUY']
            sell_confirmations = [c for c in sim.dual_confirmations if c['type'] == 'SELL']
            
            print(f"📈 BUY confirmations: {len(buy_confirmations)}")
            print(f"📉 SELL confirmations: {len(sell_confirmations)}")
            
            if sim.dual_confirmations:
                avg_confidence = sum([c['confidence'] for c in sim.dual_confirmations]) / len(sim.dual_confirmations)
                print(f"🎯 Average confirmation confidence: {avg_confidence:.1f}%")
        
        # Compare with single rule results
        print(f"\n📈 Comparison with Single Rule System:")
        print(f"   • Single rule: 43 trades, 0% win rate, -0.96% profit")
        print(f"   • Dual confirm: {len(sim.trades)} trades, {results['win_rate']:.1f}% win rate, {results['profit_percentage']:.2f}% profit")
        
        if results['win_rate'] > 10:
            print("✅ SUCCESS: Dual confirmation significantly improved win rate!")
        elif results['profit_percentage'] > -0.5:
            print("✅ IMPROVEMENT: Better than single rule system!")
        elif len(sim.trades) > 0:
            print("⚠️ MIXED: Some improvement but needs fine-tuning")
        else:
            print("⚠️ LOW ACTIVITY: Too restrictive, may need adjustment")
        
        # Activity assessment
        if len(sim.trades) > 0:
            daily_trades = len(sim.trades) / 3
            if daily_trades >= 5:
                print(f"✅ GOOD ACTIVITY: {daily_trades:.1f} trades/day")
            elif daily_trades >= 2:
                print(f"✅ MODERATE ACTIVITY: {daily_trades:.1f} trades/day")
            else:
                print(f"⚠️ LOW ACTIVITY: {daily_trades:.1f} trades/day")
        
        print("\n🎯 Final Assessment:")
        if results['profit_percentage'] > 0 and results['win_rate'] > 30:
            print("🏆 EXCELLENT: Dual confirmation system working well!")
        elif results['profit_percentage'] > -0.5 and results['win_rate'] > 10:
            print("✅ GOOD: Significant improvement over single rules")
        elif len(sim.trades) > 5:
            print("⚡ ACTIVE: Good activity with room for improvement")
        else:
            print("🔧 NEEDS WORK: May need to adjust confirmation requirements")
            
        return results
    else:
        print("❌ Dual confirmation test failed")
        return None

if __name__ == "__main__":
    test_dual_confirmation_system()
