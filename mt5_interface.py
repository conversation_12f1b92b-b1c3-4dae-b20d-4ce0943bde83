# mt5_interface.py

import MetaTrader5 as mt5
import pandas as pd
import time


# --- تهيئة الاتصال بـ MetaTrader 5 ---
def initialize_mt5_connection():
    """
    يهيئ الاتصال بمنصة MetaTrader 5.
    يجب أن تكون منصة MT5 مفتوحة وأن تكون مسجل الدخول لحسابك التجريبي.
    """
    if not mt5.initialize():
        print(
            f"Failed to initialize MetaTrader5 connection. Error code: {mt5.last_error()}"
        )
        print(
            "Please ensure MT5 terminal is open and you are logged in to your demo account."
        )
        return False
    else:
        print("MetaTrader5 connection initialized successfully.")
        return True


# --- إغلاق الاتصال بـ MetaTrader 5 ---
def shutdown_mt5_connection():
    """
    يغلق الاتصال بمنصة MetaTrader 5.
    """
    mt5.shutdown()
    print("\nMetaTrader5 connection shutdown.")


# --- جلب معلومات الحساب ---
def get_account_details():
    """
    يجلب ويعرض تفاصيل الحساب المتصل.
    """
    account_info = mt5.account_info()
    if account_info:
        print("\n--- Account Details ---")
        account_info_dict = account_info._asdict()
        for key, value in account_info_dict.items():
            if "_time" in key and isinstance(value, int):
                print(
                    f"{key.replace('_', ' ').title()}: {pd.to_datetime(value, unit='s')}"
                )
            else:
                print(f"{key.replace('_', ' ').title()}: {value}")
        return account_info
    else:
        print(f"Failed to get account info. Error code: {mt5.last_error()}")
        return None


# --- جلب أسعار السيمبول الحالية ---
def get_current_prices(symbol):
    """
    يجلب ويعرض أحدث أسعار Bid و Ask لرمز معين.
    """
    if not mt5.symbol_select(symbol, True):
        print(f"Failed to select symbol '{symbol}'. Error code: {mt5.last_error()}")
        print(f"Please ensure '{symbol}' is added to your MT5 Market Watch.")
        return None

    last_tick = mt5.symbol_info_tick(symbol)
    if last_tick:
        # print(f"\n--- Current {symbol} Prices ---")
        # print(f"Time: {pd.to_datetime(last_tick.time, unit='s')}")
        # print(f"Bid: {last_tick.bid}")
        # print(f"Ask: {last_tick.ask}")
        # print(f"Spread: {last_tick.ask - last_tick.bid:.5f}")
        return last_tick
    else:
        print(f"Failed to get tick data for '{symbol}'. Error code: {mt5.last_error()}")
        return None


# --- جلب البيانات التاريخية (الشموع) ---
def get_historical_data(symbol, timeframe, num_candles):
    """
    يجلب بيانات الشموع التاريخية لرمز وإطار زمني محدد.
    """
    # print(f"--- Fetching last {num_candles} {str(timeframe).split('_')[-1]} {symbol} Candles ---")
    rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, num_candles)

    if rates is not None and len(rates) > 0:
        rates_frame = pd.DataFrame(rates)
        rates_frame["time"] = pd.to_datetime(rates_frame["time"], unit="s")
        return rates_frame
    else:
        # print(f"Failed to get historical data for '{symbol}'. Error code: {mt5.last_error()}")
        return None


# --- وضع أمر تداول (شراء أو بيع) ---
def place_trade_order(
    symbol, trade_type, volume, slippage, magic_number, stop_loss=0.0, take_profit=0.0
):
    """
    يضع أمر تداول (شراء أو بيع) في MT5.
    :param symbol: رمز الأداة المالية.
    :param trade_type: نوع التداول (mt5.ORDER_TYPE_BUY للشراء، mt5.ORDER_TYPE_SELL للبيع).
    :param volume: حجم اللوت للتداول.
    :param slippage: الحد الأقصى للانزلاق السعري المسموح به (بالنقاط).
    :param magic_number: رقم سحري لتعريف الأوامر.
    :param stop_loss: سعر وقف الخسارة (اختياري).
    :param take_profit: سعر جني الأرباح (اختياري).
    :return: نتيجة الطلب من mt5.order_send() أو None إذا فشل.
    """

    symbol_info = mt5.symbol_info(symbol)
    if symbol_info is None:
        print(f"Symbol '{symbol}' not found.")
        return None

    price = (
        get_current_prices(symbol).ask
        if trade_type == mt5.ORDER_TYPE_BUY
        else get_current_prices(symbol).bid
    )

    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": volume,
        "type": trade_type,
        "price": price,
        "deviation": slippage,
        "magic": magic_number,
        "comment": "My_Gold_Bot_Trade",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_FOK,  # Fill Or Kill (ينفذ بالكامل أو لا ينفذ)
    }

    if stop_loss != 0.0:
        request["sl"] = stop_loss
    if take_profit != 0.0:
        request["tp"] = take_profit

    trade_type_str = "BUY" if trade_type == mt5.ORDER_TYPE_BUY else "SELL"
    print(
        f"\n--- Attempting to place a {trade_type_str} order for {volume} lots of {symbol} at price {price:.3f} ---"
    )
    result = mt5.order_send(request)

    if result:
        print("Order sent successfully!")
        print(f"Order Result: {result}")
        print(f"Return Code: {result.retcode}")
        if result.retcode == 10009 or result.retcode == 10008:
            print(f"Order executed/accepted! Deal Ticket: {result.deal} (if executed)")
        else:
            print(f"Order failed with code {result.retcode}: {result.comment}")
            print(f"Order request info: {result.request_id} - {result.request}")
    else:
        print(f"Order sending failed completely. Error code: {mt5.last_error()}")

    return result
