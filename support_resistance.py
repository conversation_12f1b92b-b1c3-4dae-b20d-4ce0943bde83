# support_resistance.py

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
try:
    # محاولة الاستيراد النسبي (عند التشغيل كجزء من حزمة)
    from .config import (
        PIVOT_TIMEFRAME,
        SWING_LOOKBACK_PERIODS,
        SWING_STRENGTH,
        PIVOT_WEIGHT,
        SWING_WEIGHT,
        DEFAULT_RESISTANCE_LEVEL,
        DEFAULT_SUPPORT_LEVEL,
        SYMBOL,
    )
    from .mt5_interface import get_historical_data
except ImportError:
    # الاستيراد المباشر (عند التشغيل كملف منفصل)
    from config import (
        PIVOT_TIMEFRAME,
        SWING_LOOKBACK_PERIODS,
        SWING_STRENGTH,
        PIVOT_WEIGHT,
        SWING_WEIGHT,
        DEFAULT_RESISTANCE_LEVEL,
        DEFAULT_SUPPORT_LEVEL,
        SYMBOL,
    )
    from mt5_interface import get_historical_data


def calculate_pivot_points(symbol, timeframe=None):
    """
    حساب نقاط البيفوت (Pivot Points) بناءً على بيانات اليوم السابق.
    
    :param symbol: رمز الأداة المالية
    :param timeframe: الإطار الزمني (افتراضي: يومي)
    :return: dict يحتوي على pivot, resistance, support
    """
    if timeframe is None:
        timeframe = PIVOT_TIMEFRAME
    
    try:
        # جلب آخر 3 شموع يومية (للتأكد من وجود بيانات كافية)
        daily_data = get_historical_data(symbol, timeframe, 3)
        
        if daily_data is None or len(daily_data) < 2:
            print("فشل في جلب البيانات اليومية لحساب البيفوت")
            return None
        
        # استخدام بيانات اليوم السابق (الشمعة قبل الأخيرة)
        yesterday = daily_data.iloc[-2]
        
        high = yesterday['high']
        low = yesterday['low']
        close = yesterday['close']
        
        # حساب نقطة البيفوت الأساسية
        pivot = (high + low + close) / 3
        
        # حساب مستويات الدعم والمقاومة
        resistance1 = 2 * pivot - low
        support1 = 2 * pivot - high
        
        resistance2 = pivot + (high - low)
        support2 = pivot - (high - low)
        
        result = {
            'pivot': pivot,
            'resistance1': resistance1,
            'resistance2': resistance2,
            'support1': support1,
            'support2': support2,
            'yesterday_high': high,
            'yesterday_low': low,
            'yesterday_close': close
        }
        
        print(f"Pivot Points محسوبة: Pivot={pivot:.2f}, R1={resistance1:.2f}, S1={support1:.2f}")
        return result
        
    except Exception as e:
        print(f"خطأ في حساب Pivot Points: {e}")
        return None


def find_swing_levels(rates, lookback_periods=None, strength=None):
    """
    العثور على مستويات Swing Highs و Swing Lows.
    
    :param rates: DataFrame للشموع التاريخية
    :param lookback_periods: عدد الشموع للبحث
    :param strength: قوة النقطة (عدد الشموع على كل جانب)
    :return: dict يحتوي على swing_highs, swing_lows
    """
    if lookback_periods is None:
        lookback_periods = SWING_LOOKBACK_PERIODS
    if strength is None:
        strength = SWING_STRENGTH
    
    try:
        if rates is None or len(rates) < lookback_periods:
            print("بيانات غير كافية لحساب Swing Levels")
            return None
        
        # استخدام آخر البيانات المتاحة
        recent_data = rates.tail(lookback_periods).copy()
        
        swing_highs = []
        swing_lows = []
        
        # البحث عن Swing Highs و Swing Lows
        for i in range(strength, len(recent_data) - strength):
            current_high = recent_data.iloc[i]['high']
            current_low = recent_data.iloc[i]['low']
            
            # فحص Swing High
            is_swing_high = True
            for j in range(i - strength, i + strength + 1):
                if j != i and recent_data.iloc[j]['high'] >= current_high:
                    is_swing_high = False
                    break
            
            if is_swing_high:
                swing_highs.append(current_high)
            
            # فحص Swing Low
            is_swing_low = True
            for j in range(i - strength, i + strength + 1):
                if j != i and recent_data.iloc[j]['low'] <= current_low:
                    is_swing_low = False
                    break
            
            if is_swing_low:
                swing_lows.append(current_low)
        
        # ترتيب وأخذ أقرب المستويات
        swing_highs.sort(reverse=True)  # من الأعلى للأقل
        swing_lows.sort()  # من الأقل للأعلى
        
        result = {
            'swing_highs': swing_highs[:3],  # أعلى 3 مستويات
            'swing_lows': swing_lows[-3:],   # أقل 3 مستويات
            'total_highs_found': len(swing_highs),
            'total_lows_found': len(swing_lows)
        }
        
        print(f"Swing Levels: وُجد {len(swing_highs)} مقاومة و {len(swing_lows)} دعم")
        return result
        
    except Exception as e:
        print(f"خطأ في حساب Swing Levels: {e}")
        return None


def calculate_dynamic_support_resistance(symbol, rates=None):
    """
    حساب مستويات الدعم والمقاومة التلقائية بدمج Pivot Points و Swing Levels.
    
    :param symbol: رمز الأداة المالية
    :param rates: البيانات التاريخية (اختياري)
    :return: dict يحتوي على resistance_level, support_level
    """
    try:
        print("\n--- بدء حساب مستويات الدعم والمقاومة التلقائية ---")
        
        # حساب Pivot Points
        pivot_data = calculate_pivot_points(symbol)
        
        # حساب Swing Levels
        swing_data = find_swing_levels(rates) if rates is not None else None
        
        # الحصول على السعر الحالي للمقارنة
        current_tick = mt5.symbol_info_tick(symbol)
        current_price = current_tick.bid if current_tick else None
        
        if current_price is None:
            print("فشل في الحصول على السعر الحالي")
            return {
                'resistance_level': DEFAULT_RESISTANCE_LEVEL,
                'support_level': DEFAULT_SUPPORT_LEVEL,
                'method': 'default_fallback'
            }
        
        # دمج النتائج
        final_resistance = DEFAULT_RESISTANCE_LEVEL
        final_support = DEFAULT_SUPPORT_LEVEL
        method_used = 'default'
        
        if pivot_data and swing_data:
            # دمج كامل: Pivot + Swing
            pivot_resistance = pivot_data['resistance1']
            pivot_support = pivot_data['support1']
            
            # اختيار أقرب swing level للسعر الحالي
            swing_resistance = None
            swing_support = None
            
            # العثور على أقرب مقاومة swing أعلى من السعر الحالي
            for high in swing_data['swing_highs']:
                if high > current_price:
                    swing_resistance = high
                    break
            
            # العثور على أقرب دعم swing أقل من السعر الحالي
            for low in reversed(swing_data['swing_lows']):
                if low < current_price:
                    swing_support = low
                    break
            
            if swing_resistance and swing_support:
                final_resistance = (PIVOT_WEIGHT * pivot_resistance + 
                                  SWING_WEIGHT * swing_resistance)
                final_support = (PIVOT_WEIGHT * pivot_support + 
                               SWING_WEIGHT * swing_support)
                method_used = 'pivot_swing_combined'
            else:
                final_resistance = pivot_resistance
                final_support = pivot_support
                method_used = 'pivot_only'
                
        elif pivot_data:
            # Pivot فقط
            final_resistance = pivot_data['resistance1']
            final_support = pivot_data['support1']
            method_used = 'pivot_only'
            
        elif swing_data:
            # Swing فقط
            if swing_data['swing_highs'] and swing_data['swing_lows']:
                final_resistance = swing_data['swing_highs'][0]
                final_support = swing_data['swing_lows'][-1]
                method_used = 'swing_only'
        
        result = {
            'resistance_level': round(final_resistance, 2),
            'support_level': round(final_support, 2),
            'current_price': round(current_price, 2),
            'method': method_used,
            'pivot_data': pivot_data,
            'swing_data': swing_data
        }
        
        print(f"✅ مستويات محسوبة: مقاومة={result['resistance_level']}, دعم={result['support_level']}")
        print(f"السعر الحالي: {result['current_price']}, الطريقة: {method_used}")
        
        return result
        
    except Exception as e:
        print(f"خطأ في حساب المستويات التلقائية: {e}")
        return {
            'resistance_level': DEFAULT_RESISTANCE_LEVEL,
            'support_level': DEFAULT_SUPPORT_LEVEL,
            'method': 'error_fallback'
        }
