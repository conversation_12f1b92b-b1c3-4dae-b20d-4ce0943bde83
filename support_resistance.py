# support_resistance.py

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
try:
    # محاولة الاستيراد النسبي (عند التشغيل كجزء من حزمة)
    from .config import (
        PIVOT_TIMEFRAME,
        SWING_LOOKBACK_PERIODS,
        SWING_STRENGTH,
        PIVOT_WEIGHT,
        SWING_WEIGHT,
        DEFAULT_RESISTANCE_LEVEL,
        DEFAULT_SUPPORT_LEVEL,
        SYMBOL,
    )
    from .mt5_interface import get_historical_data
except ImportError:
    # الاستيراد المباشر (عند التشغيل كملف منفصل)
    from config import (
        PIVOT_TIMEFRAME,
        SWING_LOOKBACK_PERIODS,
        SWING_STRENGTH,
        PIVOT_WEIGHT,
        SWING_WEIGHT,
        DEFAULT_RESISTANCE_LEVEL,
        DEFAULT_SUPPORT_LEVEL,
        SYMBOL,
    )
    from mt5_interface import get_historical_data


def calculate_pivot_points(symbol, timeframe=None):
    """
    Calculate Pivot Points based on previous day's data.

    :param symbol: Financial instrument symbol
    :param timeframe: Timeframe (default: daily)
    :return: dict containing pivot, resistance, support
    """
    if timeframe is None:
        timeframe = PIVOT_TIMEFRAME
    
    try:
        # Get last 3 daily candles (to ensure sufficient data)
        daily_data = get_historical_data(symbol, timeframe, 3)

        if daily_data is None or len(daily_data) < 2:
            print("Failed to get daily data for pivot calculation")
            return None

        # Use previous day's data (second to last candle)
        yesterday = daily_data.iloc[-2]
        
        high = yesterday['high']
        low = yesterday['low']
        close = yesterday['close']

        # Calculate basic pivot point
        pivot = (high + low + close) / 3

        # Calculate support and resistance levels
        resistance1 = 2 * pivot - low
        support1 = 2 * pivot - high

        resistance2 = pivot + (high - low)
        support2 = pivot - (high - low)
        
        result = {
            'pivot': pivot,
            'resistance1': resistance1,
            'resistance2': resistance2,
            'support1': support1,
            'support2': support2,
            'yesterday_high': high,
            'yesterday_low': low,
            'yesterday_close': close
        }
        
        print(f"Pivot Points calculated: Pivot={pivot:.2f}, R1={resistance1:.2f}, S1={support1:.2f}")
        return result

    except Exception as e:
        print(f"Error calculating Pivot Points: {e}")
        return None


def find_swing_levels(rates, lookback_periods=None, strength=None):
    """
    Find Swing Highs and Swing Lows levels.

    :param rates: DataFrame of historical candles
    :param lookback_periods: Number of candles to look back
    :param strength: Point strength (number of candles on each side)
    :return: dict containing swing_highs, swing_lows
    """
    if lookback_periods is None:
        lookback_periods = SWING_LOOKBACK_PERIODS
    if strength is None:
        strength = SWING_STRENGTH
    
    try:
        if rates is None or len(rates) < lookback_periods:
            print("Insufficient data for Swing Levels calculation")
            return None

        # Use latest available data
        recent_data = rates.tail(lookback_periods).copy()

        swing_highs = []
        swing_lows = []

        # Search for Swing Highs and Swing Lows
        for i in range(strength, len(recent_data) - strength):
            current_high = recent_data.iloc[i]['high']
            current_low = recent_data.iloc[i]['low']

            # Check Swing High
            is_swing_high = True
            for j in range(i - strength, i + strength + 1):
                if j != i and recent_data.iloc[j]['high'] >= current_high:
                    is_swing_high = False
                    break

            if is_swing_high:
                swing_highs.append(current_high)

            # Check Swing Low
            is_swing_low = True
            for j in range(i - strength, i + strength + 1):
                if j != i and recent_data.iloc[j]['low'] <= current_low:
                    is_swing_low = False
                    break

            if is_swing_low:
                swing_lows.append(current_low)

        # Sort and take closest levels
        swing_highs.sort(reverse=True)  # From highest to lowest
        swing_lows.sort()  # From lowest to highest
        
        result = {
            'swing_highs': swing_highs[:3],  # Top 3 levels
            'swing_lows': swing_lows[-3:],   # Bottom 3 levels
            'total_highs_found': len(swing_highs),
            'total_lows_found': len(swing_lows)
        }

        print(f"Swing Levels: Found {len(swing_highs)} resistance and {len(swing_lows)} support")
        return result

    except Exception as e:
        print(f"Error calculating Swing Levels: {e}")
        return None


def calculate_dynamic_support_resistance(symbol, rates=None):
    """
    Calculate automatic support and resistance levels by combining Pivot Points and Swing Levels.

    :param symbol: Financial instrument symbol
    :param rates: Historical data (optional)
    :return: dict containing resistance_level, support_level
    """
    try:
        print("\n--- Starting automatic support/resistance calculation ---")

        # Calculate Pivot Points
        pivot_data = calculate_pivot_points(symbol)

        # Calculate Swing Levels
        swing_data = find_swing_levels(rates) if rates is not None else None

        # Get current price for comparison
        current_tick = mt5.symbol_info_tick(symbol)
        current_price = current_tick.bid if current_tick else None

        if current_price is None:
            print("Failed to get current price")
            return {
                'resistance_level': DEFAULT_RESISTANCE_LEVEL,
                'support_level': DEFAULT_SUPPORT_LEVEL,
                'method': 'default_fallback'
            }
        
        # Combine results
        final_resistance = DEFAULT_RESISTANCE_LEVEL
        final_support = DEFAULT_SUPPORT_LEVEL
        method_used = 'default'

        if pivot_data and swing_data:
            # Full combination: Pivot + Swing
            pivot_resistance = pivot_data['resistance1']
            pivot_support = pivot_data['support1']

            # Choose closest swing level to current price
            swing_resistance = None
            swing_support = None

            # Find closest swing resistance above current price
            for high in swing_data['swing_highs']:
                if high > current_price:
                    swing_resistance = high
                    break

            # Find closest swing support below current price
            for low in reversed(swing_data['swing_lows']):
                if low < current_price:
                    swing_support = low
                    break

            if swing_resistance and swing_support:
                final_resistance = (PIVOT_WEIGHT * pivot_resistance +
                                  SWING_WEIGHT * swing_resistance)
                final_support = (PIVOT_WEIGHT * pivot_support +
                               SWING_WEIGHT * swing_support)
                method_used = 'pivot_swing_combined'
            else:
                final_resistance = pivot_resistance
                final_support = pivot_support
                method_used = 'pivot_only'

        elif pivot_data:
            # Pivot only
            final_resistance = pivot_data['resistance1']
            final_support = pivot_data['support1']
            method_used = 'pivot_only'

        elif swing_data:
            # Swing only
            if swing_data['swing_highs'] and swing_data['swing_lows']:
                final_resistance = swing_data['swing_highs'][0]
                final_support = swing_data['swing_lows'][-1]
                method_used = 'swing_only'
        
        result = {
            'resistance_level': round(final_resistance, 2),
            'support_level': round(final_support, 2),
            'current_price': round(current_price, 2),
            'method': method_used,
            'pivot_data': pivot_data,
            'swing_data': swing_data
        }
        
        print(f"✅ Levels calculated: resistance={result['resistance_level']}, support={result['support_level']}")
        print(f"Current price: {result['current_price']}, method: {method_used}")

        return result

    except Exception as e:
        print(f"Error in automatic levels calculation: {e}")
        return {
            'resistance_level': DEFAULT_RESISTANCE_LEVEL,
            'support_level': DEFAULT_SUPPORT_LEVEL,
            'method': 'error_fallback'
        }
