# strategy.py
# Advanced multi-indicator trading strategy

import pandas as pd
from datetime import datetime

try:
    from .config import (
        RSI_OVERBOUGHT, RSI_OVERSOLD, RSI_NEUTRAL_HIGH, RSI_NEUTRAL_LOW,
        MAGIC_NUMBER, SLIPPAGE
    )
    from .technical_indicators import (
        get_all_indicators, get_entry_signals, analyze_trend_strength, print_indicator_summary
    )
    from .mt5_interface import place_trade_order, get_current_prices
except ImportError:
    from config import (
        RSI_OVERBOUGHT, RSI_OVERSOLD, RSI_NEUTRAL_HIGH, RSI_NEUTRAL_LOW,
        MAGIC_NUMBER, SLIPPAGE
    )
    from technical_indicators import (
        get_all_indicators, get_entry_signals, analyze_trend_strength, print_indicator_summary
    )
    from mt5_interface import place_trade_order, get_current_prices


def is_trading_allowed_by_time(current_time):
    """
    Enhanced time-based trading filter
    """
    hour = current_time.hour
    
    # News filtering disabled for more trading opportunities
    # news_hours = [14]  # Disabled

    # if hour in news_hours:
    #     print(f"Trading restricted due to news window: {current_time.strftime('%H:%M:%S')}")
    #     return False
    
    # Avoid low liquidity hours (optional)
    # if hour < 6 or hour > 22:  # Avoid very early/late hours
    #     print(f"Trading restricted due to low liquidity: {current_time.strftime('%H:%M:%S')}")
    #     return False
    
    return True


def check_advanced_trade_signal(rates, current_time, resistance_level, support_level, debug=False):
    """
    Advanced signal detection using multiple technical indicators
    """
    # Time-based filtering
    if not is_trading_allowed_by_time(current_time):
        return "NO_SIGNAL", 0
    
    # Calculate all technical indicators
    indicators = get_all_indicators(rates)
    if not indicators:
        print("Failed to calculate indicators")
        return "NO_SIGNAL", 0
    
    # Debug output
    if debug:
        print_indicator_summary(indicators)
    
    # Get entry signals with confidence
    signal, confidence = get_entry_signals(indicators, resistance_level, support_level)
    
    # Additional filters for signal quality
    if signal != "NO_SIGNAL":
        # Check if we're in a strong trend
        trend_strength = analyze_trend_strength(indicators)
        
        # Boost confidence for strong trends
        if "STRONG" in trend_strength:
            confidence += 10
        
        # Reduce confidence for neutral trends (reduced penalty)
        if trend_strength == "NEUTRAL":
            confidence -= 8  # Reduced from 15 to 8
        
        # Ultra aggressive confidence filtering (like your friend's bot)
        if confidence < 35:  # Ultra low minimum threshold
            signal = "NO_SIGNAL"
        # No other filtering for maximum opportunities
    
    print(f"Signal: {signal}, Confidence: {confidence:.1f}%, Trend: {analyze_trend_strength(indicators)}")
    
    return signal, confidence


def manage_advanced_positions(symbol, trade_signal, confidence, trade_volume, current_open_position_type):
    """
    Advanced position management with confidence-based decisions
    """
    import MetaTrader5 as mt5
    
    # Get current positions
    positions = mt5.positions_get(symbol=symbol)
    
    # Update actual position type
    actual_position_type = None
    if positions:
        for pos in positions:
            if pos.type == mt5.ORDER_TYPE_BUY:
                actual_position_type = "BUY"
            elif pos.type == mt5.ORDER_TYPE_SELL:
                actual_position_type = "SELL"
    
    # No position currently open
    if not actual_position_type:
        if trade_signal == "BUY":
            print(f"Opening BUY position with {confidence:.1f}% confidence")
            place_trade_order(symbol, mt5.ORDER_TYPE_BUY, trade_volume, SLIPPAGE, MAGIC_NUMBER)
            return "BUY"
        elif trade_signal == "SELL":
            print(f"Opening SELL position with {confidence:.1f}% confidence")
            place_trade_order(symbol, mt5.ORDER_TYPE_SELL, trade_volume, SLIPPAGE, MAGIC_NUMBER)
            return "SELL"
        return None
    
    # Position is open - check for opposite signals
    for position in positions:
        # High confidence opposite signal - close and reverse
        if confidence >= 75:  # Only reverse on very high confidence
            if (trade_signal == "BUY" and actual_position_type == "SELL") or \
               (trade_signal == "SELL" and actual_position_type == "BUY"):
                
                print(f"High confidence opposite signal ({confidence:.1f}%). Closing {actual_position_type} position.")
                
                # Close current position
                current_tick = get_current_prices(symbol)
                if current_tick is None:
                    print(f"Could not get current tick for {symbol}")
                    return actual_position_type
                
                close_price = current_tick.ask if position.type == mt5.ORDER_TYPE_BUY else current_tick.bid
                
                close_request = {
                    "action": mt5.TRADE_ACTION_DEAL,
                    "symbol": symbol,
                    "volume": position.volume,
                    "type": mt5.ORDER_TYPE_SELL if position.type == mt5.ORDER_TYPE_BUY else mt5.ORDER_TYPE_BUY,
                    "position": position.ticket,
                    "price": close_price,
                    "slippage": SLIPPAGE,
                    "magic": MAGIC_NUMBER,
                    "comment": f"Close opposite signal {confidence:.1f}%",
                    "type_time": mt5.ORDER_TIME_GTC,
                    "type_filling": mt5.ORDER_FILLING_RETURN,
                }
                
                close_result = mt5.order_send(close_request)
                
                if close_result and (close_result.retcode == 10009 or close_result.retcode == 10008):
                    print(f"Position {position.ticket} closed successfully.")
                    
                    # Open new position in opposite direction
                    if trade_signal == "BUY":
                        place_trade_order(symbol, mt5.ORDER_TYPE_BUY, trade_volume, SLIPPAGE, MAGIC_NUMBER)
                        return "BUY"
                    elif trade_signal == "SELL":
                        place_trade_order(symbol, mt5.ORDER_TYPE_SELL, trade_volume, SLIPPAGE, MAGIC_NUMBER)
                        return "SELL"
                    return None
                else:
                    print(f"Failed to close position {position.ticket}")
                    return actual_position_type
        
        # Same direction signal - hold position
        elif (trade_signal == "BUY" and actual_position_type == "BUY") or \
             (trade_signal == "SELL" and actual_position_type == "SELL"):
            print(f"Signal matches current position. Holding {actual_position_type} position.")
            return actual_position_type
        
        # Low confidence opposite signal - hold current position
        elif trade_signal != "NO_SIGNAL":
            print(f"Low confidence opposite signal ({confidence:.1f}%). Keeping current {actual_position_type} position.")
            return actual_position_type
        
        # No signal - hold current position
        elif trade_signal == "NO_SIGNAL":
            print("No signal. Keeping current position open.")
            return actual_position_type
    
    return actual_position_type


def get_risk_management_params(confidence, base_volume):
    """
    ENHANCED: Aggressive position sizing for higher profits
    """
    try:
        from config import MAX_LOT_SIZE, VOLUME_MULTIPLIER, BASE_LOT_SIZE
        max_volume = MAX_LOT_SIZE
        base_lot = BASE_LOT_SIZE
    except ImportError:
        max_volume = 0.20
        base_lot = 0.05
        VOLUME_MULTIPLIER = 2.0

    # AGGRESSIVE scaling for higher profits
    if confidence >= 85:
        volume_multiplier = 4.0  # 4x for very high confidence (maximum profit)
    elif confidence >= 75:
        volume_multiplier = 3.0  # 3x for high confidence
    elif confidence >= 65:
        volume_multiplier = 2.0  # 2x for medium-high confidence
    elif confidence >= 55:
        volume_multiplier = 1.5  # 1.5x for medium confidence
    else:
        volume_multiplier = 1.0  # Base volume for low confidence

    # Use the larger base lot size
    adjusted_volume = max(base_volume, base_lot) * volume_multiplier

    # Ensure volume is within bounds but allow higher maximum
    min_volume = 0.01
    adjusted_volume = max(min_volume, min(adjusted_volume, max_volume))

    return round(adjusted_volume, 2)


def print_strategy_status(signal, confidence, trend_strength, current_position):
    """
    Print current strategy status for monitoring
    """
    print("\n" + "="*50)
    print("🤖 Advanced Strategy Status")
    print("="*50)
    print(f"📊 Signal: {signal}")
    print(f"🎯 Confidence: {confidence:.1f}%")
    print(f"📈 Trend: {trend_strength}")
    print(f"💼 Position: {current_position or 'None'}")
    print("="*50)
