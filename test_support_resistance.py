# test_support_resistance.py
# ملف اختبار للتأكد من عمل نظام حساب الدعم والمقاومة التلقائي

from support_resistance import calculate_dynamic_support_resistance
from config import SYMBOL, TIMEFRAME_TO_USE, NUM_CANDLES_FOR_DATA
from mt5_interface import initialize_mt5_connection, shutdown_mt5_connection, get_historical_data


def test_support_resistance_calculation():
    """
    اختبار حساب مستويات الدعم والمقاومة التلقائية
    """
    print("🧪 بدء اختبار نظام حساب الدعم والمقاومة التلقائي...")
    
    # تهيئة الاتصال بـ MT5
    if not initialize_mt5_connection():
        print("❌ فشل في الاتصال بـ MetaTrader 5")
        return False
    
    try:
        # جلب البيانات التاريخية
        print(f"📊 جلب البيانات التاريخية لـ {SYMBOL}...")
        rates = get_historical_data(SYMBOL, TIMEFRAME_TO_USE, NUM_CANDLES_FOR_DATA)
        
        if rates is None or rates.empty:
            print("❌ فشل في جلب البيانات التاريخية")
            return False
        
        print(f"✅ تم جلب {len(rates)} شمعة بنجاح")
        
        # حساب المستويات
        print("\n🎯 حساب مستويات الدعم والمقاومة...")
        sr_levels = calculate_dynamic_support_resistance(SYMBOL, rates)
        
        if sr_levels:
            print("\n📈 نتائج الحساب:")
            print(f"   🔴 مستوى المقاومة: {sr_levels['resistance_level']}")
            print(f"   🟢 مستوى الدعم: {sr_levels['support_level']}")
            print(f"   💰 السعر الحالي: {sr_levels['current_price']}")
            print(f"   🔧 طريقة الحساب: {sr_levels['method']}")
            
            # عرض تفاصيل إضافية إذا كانت متاحة
            if sr_levels.get('pivot_data'):
                pivot = sr_levels['pivot_data']
                print(f"\n📊 بيانات Pivot Points:")
                print(f"   • نقطة البيفوت: {pivot['pivot']:.2f}")
                print(f"   • مقاومة 1: {pivot['resistance1']:.2f}")
                print(f"   • دعم 1: {pivot['support1']:.2f}")
                print(f"   • أعلى أمس: {pivot['yesterday_high']:.2f}")
                print(f"   • أقل أمس: {pivot['yesterday_low']:.2f}")
            
            if sr_levels.get('swing_data'):
                swing = sr_levels['swing_data']
                print(f"\n🔄 بيانات Swing Levels:")
                print(f"   • عدد مستويات المقاومة: {swing['total_highs_found']}")
                print(f"   • عدد مستويات الدعم: {swing['total_lows_found']}")
                if swing['swing_highs']:
                    print(f"   • أعلى 3 مقاومات: {[f'{h:.2f}' for h in swing['swing_highs'][:3]]}")
                if swing['swing_lows']:
                    print(f"   • أقل 3 دعوم: {[f'{l:.2f}' for l in swing['swing_lows'][-3:]]}")
            
            # تحليل المستويات
            current_price = sr_levels['current_price']
            resistance = sr_levels['resistance_level']
            support = sr_levels['support_level']
            
            distance_to_resistance = resistance - current_price
            distance_to_support = current_price - support
            
            print(f"\n📏 تحليل المسافات:")
            print(f"   • المسافة للمقاومة: {distance_to_resistance:.2f} نقطة")
            print(f"   • المسافة للدعم: {distance_to_support:.2f} نقطة")
            
            if distance_to_resistance < distance_to_support:
                print("   🔴 السعر أقرب للمقاومة")
            else:
                print("   🟢 السعر أقرب للدعم")
            
            print("\n✅ اختبار النظام مكتمل بنجاح!")
            return True
        else:
            print("❌ فشل في حساب المستويات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ أثناء الاختبار: {e}")
        return False
    
    finally:
        shutdown_mt5_connection()


if __name__ == "__main__":
    test_support_resistance_calculation()
