#!/usr/bin/env python3
# test_individual_signals.py
# Test individual signal triggers with multiple positions

import sys
from datetime import datetime, timedelta
from simulation import AdvancedTradingSimulation

def test_individual_signals():
    """
    Test the new individual signals system
    """
    print("🔥 Testing Individual Signal Triggers System")
    print("=" * 60)
    print("📋 Features being tested:")
    print("   • Any single rule can trigger immediate trade")
    print("   • Multiple simultaneous positions allowed")
    print("   • Each rule can only have one active position")
    print("   • High confidence threshold maintained")
    print("=" * 60)
    
    # Create simulation instance
    sim = AdvancedTradingSimulation(
        initial_balance=10000,
        base_lot_size=0.01,
        spread=0.5,
        use_stop_loss=True
    )
    
    # Set aggressive parameters
    sim.max_positions = 8  # Allow more simultaneous positions
    sim.stop_loss_percentage = 0.003  # Tight stop loss for aggressive trading
    
    # Test period (last 3 days)
    end_date = datetime.now()
    start_date = end_date - timedelta(days=3)
    
    print(f"📅 Test period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    print(f"🎯 Max positions: {sim.max_positions}")
    print(f"🛡️ Stop loss: {sim.stop_loss_percentage*100:.1f}%")
    print()
    
    # Run individual signals simulation
    success = sim.run_individual_signals_simulation(start_date, end_date)
    
    if success:
        print("\n" + "🎉 Individual Signals Test Completed!")
        print("=" * 60)
        
        # Print enhanced results
        results = sim.print_advanced_results()
        
        # Additional analysis for individual signals
        print("\n📊 Individual Signals Analysis:")
        print("-" * 40)
        
        if sim.trades:
            # Group trades by rule
            rule_stats = {}
            for trade in sim.trades:
                rule = trade.get('rule_name', 'UNKNOWN')
                if rule not in rule_stats:
                    rule_stats[rule] = {'count': 0, 'profit': 0, 'wins': 0}
                
                rule_stats[rule]['count'] += 1
                rule_stats[rule]['profit'] += trade['profit']
                if trade['profit'] > 0:
                    rule_stats[rule]['wins'] += 1
            
            print(f"📈 Performance by Rule:")
            for rule, stats in rule_stats.items():
                win_rate = (stats['wins'] / stats['count']) * 100 if stats['count'] > 0 else 0
                print(f"   • {rule}: {stats['count']} trades, ${stats['profit']:.2f} profit, {win_rate:.1f}% win rate")
            
            # Position overlap analysis
            max_simultaneous = 0
            current_positions = 0
            position_timeline = []
            
            for trade in sorted(sim.trades, key=lambda x: x['entry_time']):
                position_timeline.append(('open', trade['entry_time']))
                position_timeline.append(('close', trade['exit_time']))
            
            position_timeline.sort(key=lambda x: x[1])
            
            for event, time in position_timeline:
                if event == 'open':
                    current_positions += 1
                    max_simultaneous = max(max_simultaneous, current_positions)
                else:
                    current_positions -= 1
            
            print(f"💼 Max simultaneous positions reached: {max_simultaneous}")
            
            # Compare with traditional approach
            traditional_trades = len([t for t in sim.trades if t.get('rule_name') == 'COMBINED'])
            individual_trades = len([t for t in sim.trades if t.get('rule_name') != 'COMBINED'])
            
            print(f"🔄 Individual rule trades: {individual_trades}")
            print(f"📊 Total activity increase: {len(sim.trades)} trades vs traditional ~2-3")
            
            # Success metrics
            if len(sim.trades) >= 10:
                print("✅ SUCCESS: High activity achieved (10+ trades)")
            else:
                print("⚠️ MODERATE: Medium activity achieved")
                
            if results['profit_percentage'] > 0:
                print("✅ SUCCESS: Strategy remains profitable")
            else:
                print("❌ WARNING: Strategy became unprofitable")
                
            if results['win_rate'] >= 40:
                print("✅ SUCCESS: Acceptable win rate maintained")
            else:
                print("⚠️ WARNING: Low win rate")
        
        print("\n🎯 Recommendations:")
        if len(sim.trades) >= 15:
            print("   • Excellent activity level - similar to your friend's bot!")
            print("   • Consider fine-tuning confidence thresholds")
        elif len(sim.trades) >= 8:
            print("   • Good activity level achieved")
            print("   • Consider lowering confidence threshold slightly")
        else:
            print("   • Activity still lower than target")
            print("   • Consider more aggressive parameters")
            
        return results
    else:
        print("❌ Individual signals test failed")
        return None

if __name__ == "__main__":
    test_individual_signals()
