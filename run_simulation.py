# run_simulation.py
# تشغيل محاكاة البوت

from datetime import datetime, timedelta
from simulation import TradingSimulation


def run_quick_test():
    """
    اختبار سريع على آخر أسبوع
    """
    print("🚀 اختبار سريع - آخر أسبوع")
    
    # تحديد الفترة (آخر أسبوع)
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    
    # إنشاء المحاكي
    sim = TradingSimulation(
        initial_balance=10000,  # 10,000$
        lot_size=0.01,          # 0.01 لوت
        spread=0.5              # 0.5 نقطة سبريد
    )
    
    # تشغيل المحاكاة
    success = sim.run_simulation(start_date, end_date)
    
    if success:
        results = sim.print_results()
        return results
    else:
        print("❌ فشلت المحاكاة")
        return None


def run_monthly_test():
    """
    اختبار شهري
    """
    print("🚀 اختبار شهري - آخر شهر")
    
    # تحديد الفترة (آخر شهر)
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    # إنشاء المحاكي
    sim = TradingSimulation(
        initial_balance=10000,  # 10,000$
        lot_size=0.01,          # 0.01 لوت
        spread=0.5              # 0.5 نقطة سبريد
    )
    
    # تشغيل المحاكاة
    success = sim.run_simulation(start_date, end_date)
    
    if success:
        results = sim.print_results()
        return results
    else:
        print("❌ فشلت المحاكاة")
        return None


def run_custom_test(days_back=14, initial_balance=10000, lot_size=0.01):
    """
    اختبار مخصص
    """
    print(f"🚀 اختبار مخصص - آخر {days_back} يوم")
    
    # تحديد الفترة
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days_back)
    
    # إنشاء المحاكي
    sim = TradingSimulation(
        initial_balance=initial_balance,
        lot_size=lot_size,
        spread=0.5
    )
    
    # تشغيل المحاكاة
    success = sim.run_simulation(start_date, end_date)
    
    if success:
        results = sim.print_results()
        return results
    else:
        print("❌ فشلت المحاكاة")
        return None


def run_multiple_tests():
    """
    تشغيل عدة اختبارات بإعدادات مختلفة
    """
    print("🔬 تشغيل اختبارات متعددة")
    print("="*60)
    
    test_configs = [
        {"days": 7, "balance": 10000, "lot": 0.01, "name": "أسبوع - محافظ"},
        {"days": 7, "balance": 10000, "lot": 0.02, "name": "أسبوع - متوسط"},
        {"days": 14, "balance": 10000, "lot": 0.01, "name": "أسبوعين - محافظ"},
        {"days": 30, "balance": 10000, "lot": 0.01, "name": "شهر - محافظ"},
    ]
    
    results = []
    
    for config in test_configs:
        print(f"\n🧪 اختبار: {config['name']}")
        print("-" * 40)
        
        result = run_custom_test(
            days_back=config['days'],
            initial_balance=config['balance'],
            lot_size=config['lot']
        )
        
        if result:
            result['config'] = config
            results.append(result)
    
    # ملخص المقارنة
    if results:
        print("\n" + "="*60)
        print("📊 ملخص المقارنة")
        print("="*60)
        
        for i, result in enumerate(results):
            config = result['config']
            print(f"\n{i+1}. {config['name']}:")
            print(f"   💰 الربح: {result['total_profit']:+.2f}$ ({result['profit_percentage']:+.2f}%)")
            print(f"   📊 الصفقات: {result['total_trades']} (نجاح: {result['win_rate']:.1f}%)")
            print(f"   📉 أقصى انخفاض: {result['max_drawdown']:.2f}%")
    
    return results


def interactive_menu():
    """
    قائمة تفاعلية لاختيار نوع الاختبار
    """
    while True:
        print("\n" + "="*50)
        print("🤖 محاكي البوت التلقائي")
        print("="*50)
        print("1. اختبار سريع (آخر أسبوع)")
        print("2. اختبار شهري (آخر شهر)")
        print("3. اختبار مخصص")
        print("4. اختبارات متعددة")
        print("5. خروج")
        print("-" * 50)
        
        choice = input("اختر رقم الاختبار: ").strip()
        
        if choice == "1":
            run_quick_test()
        
        elif choice == "2":
            run_monthly_test()
        
        elif choice == "3":
            try:
                days = int(input("عدد الأيام للاختبار (افتراضي 14): ") or "14")
                balance = float(input("الرصيد الابتدائي (افتراضي 10000): ") or "10000")
                lot_size = float(input("حجم اللوت (افتراضي 0.01): ") or "0.01")
                
                run_custom_test(days, balance, lot_size)
            except ValueError:
                print("❌ قيم غير صحيحة، يرجى المحاولة مرة أخرى")
        
        elif choice == "4":
            run_multiple_tests()
        
        elif choice == "5":
            print("👋 وداعاً!")
            break
        
        else:
            print("❌ اختيار غير صحيح")
        
        input("\nاضغط Enter للمتابعة...")


if __name__ == "__main__":
    # تشغيل القائمة التفاعلية
    interactive_menu()
