# main_advanced.py
# Main script with advanced multi-indicator strategy

import time
from datetime import datetime

try:
    from .config import (
        SYMBOL, TIMEFRAME_TO_USE, NUM_CANDLES_FOR_DATA
    )
    from .mt5_interface import (
        initialize_mt5_connection, shutdown_mt5_connection,
        get_account_details, get_historical_data
    )
    from .support_resistance import calculate_dynamic_support_resistance
    from .advanced_strategy import (
        check_advanced_trade_signal, manage_advanced_positions,
        get_risk_management_params, print_strategy_status
    )
    from .technical_indicators import analyze_trend_strength
except ImportError:
    from config import (
        SYMBOL, TIMEFRAME_TO_USE, NUM_CANDLES_FOR_DATA
    )
    from mt5_interface import (
        initialize_mt5_connection, shutdown_mt5_connection,
        get_account_details, get_historical_data
    )
    from support_resistance import calculate_dynamic_support_resistance
    from advanced_strategy import (
        check_advanced_trade_signal, manage_advanced_positions,
        get_risk_management_params, print_strategy_status
    )
    from technical_indicators import analyze_trend_strength


def main():
    """
    Main trading loop with advanced strategy
    """
    print("🚀 Starting Advanced Trading Bot...")
    print("="*60)
    
    # Initialize MT5 connection
    if not initialize_mt5_connection():
        print("❌ Failed to initialize MT5 connection")
        return
    
    # Get account details
    account_info = get_account_details()
    if not account_info:
        print("❌ Failed to get account information")
        shutdown_mt5_connection()
        return
    
    print(f"✅ Connected to account: {account_info['login']}")
    print(f"💰 Balance: ${account_info['balance']:.2f}")
    print(f"📊 Symbol: {SYMBOL}")
    print(f"⏰ Timeframe: M15")
    print("="*60)
    
    # Initialize variables
    current_open_position_type = None
    last_trade_minute = -1
    base_trade_volume = 0.01  # Base volume, will be adjusted by confidence
    
    try:
        while True:
            current_time = datetime.now()
            
            # Check if we're at the start of a new M15 candle
            if current_time.minute % 15 == 0 and current_time.second < 30:
                
                print(f"\n🕐 Checking signals at {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
                
                try:
                    # Get historical data
                    rates = get_historical_data(SYMBOL, TIMEFRAME_TO_USE, NUM_CANDLES_FOR_DATA)
                    if rates is None or rates.empty:
                        print("❌ Failed to get historical data")
                        time.sleep(60)
                        continue
                    
                    # Calculate dynamic support/resistance levels
                    sr_levels = calculate_dynamic_support_resistance(SYMBOL, rates)
                    current_resistance = sr_levels['resistance_level']
                    current_support = sr_levels['support_level']
                    
                    print(f"🎯 S/R Levels: Support={current_support:.2f}, Resistance={current_resistance:.2f}")
                    print(f"📊 Method: {sr_levels['method']}")
                    
                    # Get advanced trading signal
                    trade_signal, confidence = check_advanced_trade_signal(
                        rates, current_time, current_resistance, current_support, debug=False
                    )
                    
                    # Get trend strength for monitoring
                    from technical_indicators import get_all_indicators
                    indicators = get_all_indicators(rates)
                    trend_strength = analyze_trend_strength(indicators) if indicators else "UNKNOWN"
                    
                    # Print strategy status
                    print_strategy_status(trade_signal, confidence, trend_strength, current_open_position_type)
                    
                    # Prevent duplicate trading in same candle
                    if last_trade_minute != current_time.minute:
                        
                        if trade_signal != "NO_SIGNAL":
                            # Adjust volume based on confidence
                            adjusted_volume = get_risk_management_params(confidence, base_trade_volume)
                            print(f"📏 Adjusted volume: {adjusted_volume} (confidence: {confidence:.1f}%)")
                            
                            # Manage positions with advanced strategy
                            current_open_position_type = manage_advanced_positions(
                                SYMBOL, trade_signal, confidence, adjusted_volume, current_open_position_type
                            )
                        
                        last_trade_minute = current_time.minute
                    else:
                        print(f"⏭️ Trade already processed for minute {current_time.minute}. Skipping.")
                    
                    # Wait until next M15 candle
                    minutes_past_quarter = current_time.minute % 15
                    seconds_to_next_quarter = (15 - minutes_past_quarter) * 60 - current_time.second
                    sleep_duration = max(10, seconds_to_next_quarter)
                    
                    print(f"😴 Sleeping for {sleep_duration} seconds until next M15 candle...")
                    time.sleep(sleep_duration)
                
                except Exception as e:
                    print(f"❌ Error in main loop: {e}")
                    time.sleep(60)
            
            else:
                # Not at M15 candle start, wait and check again
                print(f"⏳ Waiting for M15 candle start. Current time: {current_time.strftime('%H:%M:%S')}")
                time.sleep(10)
    
    except KeyboardInterrupt:
        print("\n🛑 Bot stopped by user")
    except Exception as e:
        print(f"💥 Critical error: {e}")
    finally:
        shutdown_mt5_connection()
        print("🔌 MT5 connection closed. Bot terminated.")


def run_strategy_test():
    """
    Test the advanced strategy on recent data without placing trades
    """
    print("🧪 Testing Advanced Strategy...")
    print("="*50)
    
    if not initialize_mt5_connection():
        print("❌ Failed to initialize MT5 connection")
        return
    
    try:
        # Get recent data
        rates = get_historical_data(SYMBOL, TIMEFRAME_TO_USE, NUM_CANDLES_FOR_DATA)
        if rates is None or rates.empty:
            print("❌ Failed to get historical data")
            return
        
        # Calculate support/resistance
        sr_levels = calculate_dynamic_support_resistance(SYMBOL, rates)
        current_resistance = sr_levels['resistance_level']
        current_support = sr_levels['support_level']
        
        print(f"🎯 Current S/R: Support={current_support:.2f}, Resistance={current_resistance:.2f}")
        
        # Test signal generation
        current_time = datetime.now()
        signal, confidence = check_advanced_trade_signal(
            rates, current_time, current_resistance, current_support, debug=True
        )
        
        # Get trend analysis
        from technical_indicators import get_all_indicators
        indicators = get_all_indicators(rates)
        trend_strength = analyze_trend_strength(indicators) if indicators else "UNKNOWN"
        
        print(f"\n🎯 Test Results:")
        print(f"   Signal: {signal}")
        print(f"   Confidence: {confidence:.1f}%")
        print(f"   Trend: {trend_strength}")
        
        if signal != "NO_SIGNAL":
            adjusted_volume = get_risk_management_params(confidence, 0.01)
            print(f"   Suggested Volume: {adjusted_volume}")
        
        print("\n✅ Strategy test completed!")
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
    finally:
        shutdown_mt5_connection()


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        run_strategy_test()
    else:
        main()
