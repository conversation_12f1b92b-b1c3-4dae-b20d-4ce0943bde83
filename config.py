# config.py

import MetaTrader5 as mt5

# --- إعدادات الأداة المالية ---
SYMBOL = "XAUUSDm"

# --- إعدادات الإطار الزمني ---
TIMEFRAME_TO_USE = mt5.TIMEFRAME_M15  # شمعة كل 15 دقيقة
NUM_CANDLES_FOR_DATA = (
    200  # عدد الشموع المطلوبة لجلب البيانات (للمتوسطات والدعم/المقاومة)
)

# --- إعدادات المؤشرات الفنية المتقدمة ---

# Moving Averages (EMA for faster response)
EMA_FAST = 9          # EMA fast period
EMA_SLOW = 21         # EMA slow period
EMA_TREND = 50        # EMA for overall trend

# RSI Settings
RSI_PERIOD = 14       # RSI calculation period
RSI_OVERBOUGHT = 70   # Overbought level
RSI_OVERSOLD = 30     # Oversold level
RSI_NEUTRAL_HIGH = 60 # Upper neutral zone
RSI_NEUTRAL_LOW = 40  # Lower neutral zone

# MACD Settings
MACD_FAST = 12        # MACD fast EMA
MACD_SLOW = 26        # MACD slow EMA
MACD_SIGNAL = 9       # MACD signal line

# Bollinger Bands
BB_PERIOD = 20        # Bollinger Bands period
BB_STD = 2            # Standard deviation multiplier

# Volume Analysis
VOLUME_MA_PERIOD = 20 # Volume moving average period
VOLUME_SPIKE_RATIO = 1.5  # Volume spike threshold (1.5x average)

# Legacy MA settings (kept for compatibility)
MA_PERIOD_SHORT = EMA_FAST
MA_PERIOD_LONG = EMA_SLOW

# --- إعدادات مستويات الدعم والمقاومة (حساب تلقائي) ---
# **تم تحويل الحساب إلى تلقائي باستخدام Pivot Points + Swing Levels**

# إعدادات حساب Pivot Points
PIVOT_TIMEFRAME = mt5.TIMEFRAME_D1  # إطار زمني لحساب البيفوت (يومي)

# إعدادات حساب Swing Levels
SWING_LOOKBACK_PERIODS = 20  # عدد الشموع للبحث عن Swing Highs/Lows
SWING_STRENGTH = 3  # قوة النقطة (كم شمعة على كل جانب يجب أن تكون أقل/أعلى)

# أوزان دمج المستويات (مجموعها = 1.0)
PIVOT_WEIGHT = 0.6  # وزن Pivot Points
SWING_WEIGHT = 0.4  # وزن Swing Levels

# Default levels (fallback values)
DEFAULT_RESISTANCE_LEVEL = 2350.0
DEFAULT_SUPPORT_LEVEL = 2300.0


# --- إعدادات هوامش الملامسة والكسر (بالنقاط) ---
# سيتم تحويلها إلى قيمة سعرية فعلية باستخدام symbol_info.point
TOUCH_TOLERANCE_POINTS = 2  # 7 نقاط للملامسة (السعر ضمن +/- هذا الهامش)
BREAKOUT_CONFIRMATION_POINTS = 2  # 7 نقاط لتأكيد الكسر (الإغلاق فوق/تحت هذا الهامش)

# --- إعدادات التداول العام ---
MAGIC_NUMBER = 234000  # رقم سحري لتعريف صفقات بوتك
SLIPPAGE = 5  # الانزلاق السعري المسموح به (بالنقاط)

# --- إعدادات إدارة المخاطر (سيتم تطويرها لاحقاً) ---
# TRADE_VOLUME سيتم جلبه ديناميكياً من symbol_info.volume_min في main.py
# SL و TP سيتم إضافتهما لاحقاً
