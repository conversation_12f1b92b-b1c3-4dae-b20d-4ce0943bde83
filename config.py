# config.py

import MetaTrader5 as mt5

# --- إعدادات الأداة المالية ---
SYMBOL = "XAUUSDm"

# --- إعدادات الإطار الزمني ---
TIMEFRAME_TO_USE = mt5.TIMEFRAME_M15  # شمعة كل 15 دقيقة
NUM_CANDLES_FOR_DATA = (
    200  # عدد الشموع المطلوبة لجلب البيانات (للمتوسطات والدعم/المقاومة)
)

# --- إعدادات استراتيجية المتوسطات المتحركة (SMA) ---
MA_PERIOD_SHORT = 10  # فترة المتوسط المتحرك السريع (10 شموع M15)
MA_PERIOD_LONG = 20  # فترة المتوسط المتحرك البطيء (20 شمعة M15)

# --- إعدادات مستويات الدعم والمقاومة (حساب تلقائي) ---
# **تم تحويل الحساب إلى تلقائي باستخدام Pivot Points + Swing Levels**

# إعدادات حساب Pivot Points
PIVOT_TIMEFRAME = mt5.TIMEFRAME_D1  # إطار زمني لحساب البيفوت (يومي)

# إعدادات حساب Swing Levels
SWING_LOOKBACK_PERIODS = 20  # عدد الشموع للبحث عن Swing Highs/Lows
SWING_STRENGTH = 3  # قوة النقطة (كم شمعة على كل جانب يجب أن تكون أقل/أعلى)

# أوزان دمج المستويات (مجموعها = 1.0)
PIVOT_WEIGHT = 0.6  # وزن Pivot Points
SWING_WEIGHT = 0.4  # وزن Swing Levels

# مستويات افتراضية (في حالة فشل الحساب التلقائي)
DEFAULT_RESISTANCE_LEVEL = 2350.0
DEFAULT_SUPPORT_LEVEL = 2300.0

# --- إعدادات هوامش الملامسة والكسر (بالنقاط) ---
# سيتم تحويلها إلى قيمة سعرية فعلية باستخدام symbol_info.point
TOUCH_TOLERANCE_POINTS = 7  # 7 نقاط للملامسة (السعر ضمن +/- هذا الهامش)
BREAKOUT_CONFIRMATION_POINTS = 7  # 7 نقاط لتأكيد الكسر (الإغلاق فوق/تحت هذا الهامش)

# --- إعدادات التداول العام ---
MAGIC_NUMBER = 234000  # رقم سحري لتعريف صفقات بوتك
SLIPPAGE = 5  # الانزلاق السعري المسموح به (بالنقاط)

# --- إعدادات إدارة المخاطر (سيتم تطويرها لاحقاً) ---
# TRADE_VOLUME سيتم جلبه ديناميكياً من symbol_info.volume_min في main.py
# SL و TP سيتم إضافتهما لاحقاً
