# strategy_logic.py

import MetaTrader5 as mt5
import pandas as pd
import time
from .config import (
    MA_PERIOD_SHORT,
    MA_PERIOD_LONG,
    RESISTANCE_LEVEL,
    SUPPORT_LEVEL,
    TOUCH_TOLERANCE_POINTS,
    BREAKOUT_CONFIRMATION_POINTS,
    SYMBOL,
    MAGIC_NUMBER,
    SLIPPAGE,
)
from .mt5_interface import (
    place_trade_order,
    get_current_prices,
)  # استيراد لوظائف التداول


# --- حساب المتوسط المتحرك البسيط (SMA) ---
def calculate_sma(data_series, period):
    """
    يحسب المتوسط المتحرك البسيط لسلسلة بيانات.
    :param data_series: سلسلة Pandas تحتوي على القيم (عادة سعر الإغلاق).
    :param period: الفترة الزمنية للمتوسط المتحرك.
    :return: سلسلة Pandas بالمتوسط المتحرك.
    """
    return data_series.rolling(window=period).mean()


# --- تحديد ما إذا كان التداول مسموحاً به بناءً على الأخبار ---
def is_trading_allowed_by_news(current_time):
    """
    يتحقق مما إذا كان التداول مسموحاً به بناءً على أوقات الأخبار المحددة يدوياً.
    (مثال بسيط: تجنب التداول بين 12:00 PM و 1:00 PM بالتوقيت المحلي لـ XAUUSDm)
    :param current_time: الوقت الحالي (datetime object).
    :return: True إذا كان التداول مسموحاً، False بخلاف ذلك.
    """
    # يمكنك توسيع هذه القائمة أو ربطها بـ API لأخبار اقتصادية
    # هذا مثال بسيط جداً
    if current_time.hour == 12:  # تجنب الساعة 12 ظهراً
        print(
            f"Trading restricted due to news window: {current_time.strftime('%H:%M:%S')}"
        )
        return False
    # أضف المزيد من الشروط حسب الحاجة، مثلاً أوقات بيانات NFP
    return True


# --- تتبع ملامسات الدعم والمقاومة ---
def track_sr_touches(
    rates, current_resistance_level, current_support_level, touch_tolerance_value
):
    """
    تتبع عدد مرات ملامسة مستويات الدعم والمقاومة وإغلاق الشموع حولها.
    :param rates: DataFrame من الشموع التاريخية.
    :param current_resistance_level: مستوى المقاومة اليدوي.
    :param current_support_level: مستوى الدعم اليدوي.
    :param touch_tolerance_value: الهامش حول مستوى S/R لاعتباره ملامسة.
    :return: (resistance_touch_count, support_touch_count) محدثين.
    """
    # المتغيرات الجلوبال لن تكون هنا، بل ستمرر كمعاملات من main.py
    # لذا سنعيد القيم المحدثة
    resistance_touch_count = 0
    support_touch_count = 0

    # نراجع الشموع السابقة لتحديد الملامسات. نركز على آخر شمعتين أو ثلاث على الأقل
    # لضمان عدم وجود تداخلات فورية
    # هنا يجب أن نأخذ في الاعتبار أن عدادات الملامسات تحتاج إلى الاحتفاظ بحالتها عبر الدورات
    # لذا، الدالة ستحتاج لاستلام العدادات الحالية وإعادتها محدثة.

    # للتطبيق الأولي، سنقوم بتبسيط: سنفحص آخر شمعة فقط ونحدث العدادات بناءً عليها
    # المنطق الأكثر تعقيدًا لتتبع الملامسات التاريخية سيحتاج لحالة خارج هذه الدالة.

    # لتسهيل الأمر مبدئياً: افحص آخر شمعتين (أو أكثر قليلاً) لتحديد ملامسات قريبة
    # وقم بتحديث العدادات الخارجية.

    # هذا الجزء سيكون تحديًا حقيقياً بدون حالة خارجية (مثل قاعدة بيانات أو ملف)
    # أو إذا لم تمرر العدادات كمعاملات وتُعاد بعد كل استدعاء.
    # بما أن العدادات ستكون في main.py وتُمرر، سنعدل التوقيع.

    return 0, 0  # placeholder for now, actual implementation needs state passed in


# --- التحقق من إشارة التداول (مع دمج الشروط) ---
def check_for_trade_signal(
    rates,
    current_time,
    resistance_touch_count,
    support_touch_count,  # حالة العدادات
    point_value,
):
    """
    يتحقق من وجود إشارة شراء أو بيع بناءً على:
    1. عدم وجود أخبار.
    2. تقاطع المتوسطات المتحركة (SMA).
    3. كسر الدعم/المقاومة بعد الملامسة الثالثة.
    :param rates: DataFrame من الشموع التاريخية.
    :param current_time: الوقت الحالي.
    :param resistance_touch_count: عدد ملامسات المقاومة الحالي.
    :param support_touch_count: عدد ملامسات الدعم الحالي.
    :param point_value: قيمة النقطة للرمز.
    :return: tuple ('BUY', 'SELL', 'NO_SIGNAL') وعدادات الملامسات المحدثة.
    """

    # حساب الهوامش بناءً على قيمة النقطة
    touch_tolerance_value = TOUCH_TOLERANCE_POINTS * point_value
    breakout_confirmation_margin = BREAKOUT_CONFIRMATION_POINTS * point_value

    # 1. فلترة الأخبار
    if not is_trading_allowed_by_news(current_time):
        return "NO_SIGNAL", resistance_touch_count, support_touch_count

    # تأكد أن لدينا شموع كافية لحساب المتوسطات المتحركة
    if rates is None or rates.empty or len(rates) < MA_PERIOD_LONG:
        print("Not enough candles for MA calculation or data is empty.")
        return "NO_SIGNAL", resistance_touch_count, support_touch_count

    # 2. حساب المتوسطات المتحركة
    sma_short = calculate_sma(rates["close"], MA_PERIOD_SHORT)
    sma_long = calculate_sma(rates["close"], MA_PERIOD_LONG)

    if (
        pd.isna(sma_short.iloc[-1])
        or pd.isna(sma_long.iloc[-1])
        or pd.isna(sma_short.iloc[-2])
        or pd.isna(sma_long.iloc[-2])
    ):
        print("SMA values are NaN, not enough data or calculation error.")
        return "NO_SIGNAL", resistance_touch_count, support_touch_count

    ma_trend = "NONE"
    if sma_short.iloc[-1] > sma_long.iloc[-1]:
        ma_trend = "BULLISH"  # صعودي
    elif sma_short.iloc[-1] < sma_long.iloc[-1]:
        ma_trend = "BEARISH"  # هبوطي

    last_candle = rates.iloc[-1]  # الشمعة الأخيرة المغلقة

    # --- تحديث عدادات ملامسات الدعم والمقاومة بناءً على الشمعة الأخيرة ---
    # هذا المنطق يجب أن يكون أكثر قوة لتتبع الملامسات عبر عدة شموع.
    # للتبسيط الآن، سنقوم بتحديث العداد بناءً على الشمعة الأخيرة
    # هذا يعني أن العدادات ستعتمد فقط على الشمعة الأخيرة، وليس على تاريخ الملامسات.
    # لتطبيق "ملامستين ثم كسر"، يجب أن يكون هناك طريقة لتخزين هذه الملامسات.

    # مؤقتاً: سنفترض أن هذه الوظيفة تتلقى العدادات الحالية وتحدثها بناءً على الشمعة الأخيرة
    # وإذا حدث كسر، يتم إعادة تعيينها.

    new_resistance_touch_count = resistance_touch_count
    new_support_touch_count = support_touch_count

    # فحص المقاومة
    if (
        last_candle["high"] >= RESISTANCE_LEVEL - touch_tolerance_value
        and last_candle["close"] <= RESISTANCE_LEVEL + touch_tolerance_value
    ):
        if new_resistance_touch_count < 2:  # نزيد العداد فقط إذا لم نصل إلى 2 بعد
            new_resistance_touch_count += 1
        print(f"Resistance touched! Current count: {new_resistance_touch_count}")
    # إذا كانت الشمعة قد كسرت المقاومة بشكل حقيقي
    elif last_candle["close"] > RESISTANCE_LEVEL + breakout_confirmation_margin:
        new_resistance_touch_count = 0  # إعادة تعيين العداد بعد الكسر
        print("Resistance broken, resetting touch count.")

    # فحص الدعم
    if (
        last_candle["low"] <= SUPPORT_LEVEL + touch_tolerance_value
        and last_candle["close"] >= SUPPORT_LEVEL - touch_tolerance_value
    ):
        if new_support_touch_count < 2:  # نزيد العداد فقط إذا لم نصل إلى 2 بعد
            new_support_touch_count += 1
        print(f"Support touched! Current count: {new_support_touch_count}")
    # إذا كانت الشمعة قد كسرت الدعم بشكل حقيقي
    elif last_candle["close"] < SUPPORT_LEVEL - breakout_confirmation_margin:
        new_support_touch_count = 0  # إعادة تعيين العداد بعد الكسر
        print("Support broken, resetting touch count.")

    # --- منطق إشارة الشراء ---
    # إذا كان عدد ملامسات المقاومة 2 (أو أكثر)، وتم كسرها صعودًا في الشمعة الأخيرة
    if (
        new_resistance_touch_count >= 2
        and last_candle["close"] > RESISTANCE_LEVEL + breakout_confirmation_margin
    ):
        # فلترة إضافية: يجب أن يكون الاتجاه العام صعودياً (MA)
        if ma_trend == "BULLISH":
            print(
                f"BUY Signal! Resistance broken after {new_resistance_touch_count} touches. MA Trend: {ma_trend}"
            )
            # عند إعطاء إشارة شراء، يجب إعادة تعيين عداد المقاومة لانتظار دورة جديدة
            new_resistance_touch_count = 0
            new_support_touch_count = 0  # أيضاً لإعادة تعيين الدعم (للبدء من جديد)
            return "BUY", new_resistance_touch_count, new_support_touch_count

    # --- منطق إشارة البيع ---
    # إذا كان عدد ملامسات الدعم 2 (أو أكثر)، وتم كسرها هبوطًا في الشمعة الأخيرة
    if (
        new_support_touch_count >= 2
        and last_candle["close"] < SUPPORT_LEVEL - breakout_confirmation_margin
    ):
        # فلترة إضافية: يجب أن يكون الاتجاه العام هبوطياً (MA)
        if ma_trend == "BEARISH":
            print(
                f"SELL Signal! Support broken after {new_support_touch_count} touches. MA Trend: {ma_trend}"
            )
            # عند إعطاء إشارة بيع، يجب إعادة تعيين عداد الدعم
            new_support_touch_count = 0
            new_resistance_touch_count = 0  # أيضاً لإعادة تعيين المقاومة
            return "SELL", new_resistance_touch_count, new_support_touch_count

    return "NO_SIGNAL", new_resistance_touch_count, new_support_touch_count


# --- إدارة المراكز المفتوحة (Positions) ---
def manage_positions(symbol, trade_signal, trade_volume, current_open_position_type):
    """
    يدير المراكز المفتوحة: يغلق المراكز المعاكسة ويفتح مراكز جديدة بناءً على الإشارة.
    :param symbol: رمز الأداة المالية.
    :param trade_signal: الإشارة التي تم الحصول عليها ('BUY', 'SELL', 'NO_SIGNAL').
    :param trade_volume: حجم التداول المطلوب.
    :param current_open_position_type: نوع المركز المفتوح حالياً ('BUY', 'SELL', None).
    :return: نوع المركز المفتوح الجديد ('BUY', 'SELL', None).
    """

    positions = mt5.positions_get(symbol=symbol)

    # تحديث نوع المركز المفتوح الحالي بناءً على المراكز الفعلية في المنصة
    # هذا الجزء مهم لضمان أن البوت يعرف ما هو المركز المفتوح فعليًا
    actual_position_type = None
    if positions:
        for pos in positions:
            if pos.type == mt5.ORDER_TYPE_BUY:
                actual_position_type = "BUY"
            elif pos.type == mt5.ORDER_TYPE_SELL:
                actual_position_type = "SELL"
            # إذا كان هناك أكثر من مركز، هذا المنطق سيختار آخر مركز وجده
            # للحالات الأكثر تعقيدًا مع مراكز متعددة، نحتاج لمنطق أكثر دقة

    # إذا لا يوجد مركز مفتوح حالياً في المنصة
    if not actual_position_type:
        current_open_position_type = None  # تأكيد عدم وجود مركز مسجل
        if trade_signal == "BUY":
            print("No open position, BUY signal received. Placing BUY order.")
            place_trade_order(
                symbol, mt5.ORDER_TYPE_BUY, trade_volume, SLIPPAGE, MAGIC_NUMBER
            )
            return "BUY"
        elif trade_signal == "SELL":
            print("No open position, SELL signal received. Placing SELL order.")
            place_trade_order(
                symbol, mt5.ORDER_TYPE_SELL, trade_volume, SLIPPAGE, MAGIC_NUMBER
            )
            return "SELL"
        return None  # لا يوجد مركز بعد هذا الدورة

    # إذا كان هناك مركز مفتوح
    for position in positions:  # نعالج كل مركز مفتوح

        # إذا كانت الإشارة تعاكس المركز المفتوح
        if (trade_signal == "BUY" and actual_position_type == "SELL") or (
            trade_signal == "SELL" and actual_position_type == "BUY"
        ):
            print(
                f"Opposite signal ({trade_signal}) received. Closing current {actual_position_type} position."
            )

            # تحديد سعر الإغلاق الصحيح
            current_tick = get_current_prices(symbol)
            if current_tick is None:
                print(
                    f"Could not get current tick for {symbol} to close position {position.ticket}."
                )
                return actual_position_type  # لا نغير حالة المركز إذا لم نستطع الإغلاق

            close_price = (
                current_tick.bid
                if position.type == mt5.ORDER_TYPE_BUY
                else current_tick.ask
            )

            close_request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": position.symbol,
                "volume": position.volume,
                "type": (
                    mt5.ORDER_TYPE_SELL
                    if position.type == mt5.ORDER_TYPE_BUY
                    else mt5.ORDER_TYPE_BUY
                ),
                "position": position.ticket,
                "price": close_price,
                "deviation": SLIPPAGE,
                "magic": MAGIC_NUMBER,
                "comment": "Close_Bot_Position",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_RETURN,  # يسمح بالتنفيذ الجزئي
            }
            close_result = mt5.order_send(close_request)

            if close_result and (
                close_result.retcode == 10009 or close_result.retcode == 10008
            ):
                print(f"Position {position.ticket} closed successfully.")
                # بعد الإغلاق، حاول فتح المركز الجديد
                if trade_signal == "BUY":
                    place_trade_order(
                        symbol, mt5.ORDER_TYPE_BUY, trade_volume, SLIPPAGE, MAGIC_NUMBER
                    )
                    return "BUY"
                elif trade_signal == "SELL":
                    place_trade_order(
                        symbol,
                        mt5.ORDER_TYPE_SELL,
                        trade_volume,
                        SLIPPAGE,
                        MAGIC_NUMBER,
                    )
                    return "SELL"
                return None  # تم الإغلاق ولم يتم فتح صفقة جديدة بنجاح
            else:
                print(
                    f"Failed to close position {position.ticket}. Error code: {close_result.retcode if close_result else mt5.last_error()}"
                )
                return actual_position_type  # نعود بنفس حالة المركز إذا فشل الإغلاق

        # إذا كانت الإشارة بنفس اتجاه المركز المفتوح (لا تفعل شيئاً)
        elif (trade_signal == "BUY" and actual_position_type == "BUY") or (
            trade_signal == "SELL" and actual_position_type == "SELL"
        ):
            print(
                f"Signal ({trade_signal}) matches current open position. No action needed."
            )
            return actual_position_type  # نعود بنفس حالة المركز

        # إذا كانت لا توجد إشارة وتوجد صفقات مفتوحة، فلا تفعل شيئًا (حافظ على المركز)
        elif trade_signal == "NO_SIGNAL":
            print("No signal, keeping current position open.")
            return actual_position_type

    return actual_position_type  # نرجع الحالة النهائية
