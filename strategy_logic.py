# strategy_logic.py

import MetaTrader5 as mt5
import pandas as pd
try:
    # محاولة الاستيراد النسبي (عند التشغيل كجزء من حزمة)
    from .config import (
        MA_PERIOD_SHORT,
        MA_PERIOD_LONG,
        TOUCH_TOLERANCE_POINTS,
        BREAKOUT_CONFIRMATION_POINTS,
        MAGIC_NUMBER,
        SLIPPAGE,
    )
    from .mt5_interface import (
        place_trade_order,
        get_current_prices,
    )  # استيراد لوظائف التداول
except ImportError:
    # الاستيراد المباشر (عند التشغيل كملف منفصل)
    from config import (
        MA_PERIOD_SHORT,
        MA_PERIOD_LONG,
        TOUCH_TOLERANCE_POINTS,
        BREAKOUT_CONFIRMATION_POINTS,
        MAGIC_NUMBER,
        SLIPPAGE,
    )
    from mt5_interface import (
        place_trade_order,
        get_current_prices,
    )  # استيراد لوظائف التداول


# --- حساب المتوسط المتحرك البسيط (SMA) ---
def calculate_sma(data_series, period):
    """
    Calculate Simple Moving Average for a data series.
    :param data_series: Pandas series containing values (usually closing prices).
    :param period: Time period for the moving average.
    :return: Pandas series with the moving average.
    """
    return data_series.rolling(window=period).mean()


# --- تحديد ما إذا كان التداول مسموحاً به بناءً على الأخبار ---
def is_trading_allowed_by_news(current_time):
    """
    Check if trading is allowed based on manually defined news times.
    (Simple example: avoid trading between 12:00 PM and 1:00 PM local time for XAUUSDm)
    :param current_time: Current time (datetime object).
    :return: True if trading is allowed, False otherwise.
    """
    # You can expand this list or connect it to an economic news API
    # This is a very simple example
    if current_time.hour == 12:  # Avoid 12 PM hour
        print(
            f"Trading restricted due to news window: {current_time.strftime('%H:%M:%S')}"
        )
        return False
    # Add more conditions as needed, e.g., NFP data times
    return True



# --- التحقق من إشارة التداول (مع دمج الشروط) ---
def check_for_trade_signal(
    rates,
    current_time,
    resistance_touch_count,
    support_touch_count,  # Counter states
    point_value,
    resistance_level,  # Dynamically calculated resistance level
    support_level,     # Dynamically calculated support level
):
    """
    Check for buy or sell signal based on:
    1. No news events.
    2. Moving average crossover (SMA).
    3. Support/Resistance breakout after third touch.
    :param rates: DataFrame of historical candles.
    :param current_time: Current time.
    :param resistance_touch_count: Current resistance touch count.
    :param support_touch_count: Current support touch count.
    :param point_value: Point value for the symbol.
    :param resistance_level: Dynamically calculated resistance level.
    :param support_level: Dynamically calculated support level.
    :return: tuple ('BUY', 'SELL', 'NO_SIGNAL') and updated touch counters.
    """

    # Calculate margins based on point value
    touch_tolerance_value = TOUCH_TOLERANCE_POINTS * point_value
    breakout_confirmation_margin = BREAKOUT_CONFIRMATION_POINTS * point_value

    # 1. News filtering
    if not is_trading_allowed_by_news(current_time):
        return "NO_SIGNAL", resistance_touch_count, support_touch_count

    # Ensure we have enough candles for moving average calculation
    if rates is None or rates.empty or len(rates) < MA_PERIOD_LONG:
        print("Not enough candles for MA calculation or data is empty.")
        return "NO_SIGNAL", resistance_touch_count, support_touch_count

    # 2. Calculate moving averages
    sma_short = calculate_sma(rates["close"], MA_PERIOD_SHORT)
    sma_long = calculate_sma(rates["close"], MA_PERIOD_LONG)

    if (
        pd.isna(sma_short.iloc[-1])
        or pd.isna(sma_long.iloc[-1])
        or pd.isna(sma_short.iloc[-2])
        or pd.isna(sma_long.iloc[-2])
    ):
        print("SMA values are NaN, not enough data or calculation error.")
        return "NO_SIGNAL", resistance_touch_count, support_touch_count

    ma_trend = "NONE"
    if sma_short.iloc[-1] > sma_long.iloc[-1]:
        ma_trend = "BULLISH"  # Bullish trend
    elif sma_short.iloc[-1] < sma_long.iloc[-1]:
        ma_trend = "BEARISH"  # Bearish trend

    last_candle = rates.iloc[-1]  # Last closed candle

    # --- Update support/resistance touch counters based on last candle ---
    # This logic should be more robust to track touches across multiple candles.
    # For simplicity now, we'll update the counter based on the last candle
    # This means counters will depend only on the last candle, not on touch history.
    # To implement "two touches then break", there should be a way to store these touches.

    # Temporarily: we'll assume this function receives current counters and updates them based on last candle
    # and if a break occurs, they are reset.

    new_resistance_touch_count = resistance_touch_count
    new_support_touch_count = support_touch_count

    # Check resistance
    if (
        last_candle["high"] >= resistance_level - touch_tolerance_value
        and last_candle["close"] <= resistance_level + touch_tolerance_value
    ):
        if new_resistance_touch_count < 2:  # Increase counter only if we haven't reached 2 yet
            new_resistance_touch_count += 1
        print(f"Resistance touched! Current count: {new_resistance_touch_count}")
    # If candle has truly broken resistance
    elif last_candle["close"] > resistance_level + breakout_confirmation_margin:
        new_resistance_touch_count = 0  # Reset counter after break
        print("Resistance broken, resetting touch count.")

    # Check support
    if (
        last_candle["low"] <= support_level + touch_tolerance_value
        and last_candle["close"] >= support_level - touch_tolerance_value
    ):
        if new_support_touch_count < 2:  # Increase counter only if we haven't reached 2 yet
            new_support_touch_count += 1
        print(f"Support touched! Current count: {new_support_touch_count}")
    # If candle has truly broken support
    elif last_candle["close"] < support_level - breakout_confirmation_margin:
        new_support_touch_count = 0  # Reset counter after break
        print("Support broken, resetting touch count.")

    # --- Buy signal logic ---
    # If resistance touch count is 2 (or more), and it was broken upward in the last candle
    if (
        new_resistance_touch_count >= 2
        and last_candle["close"] > resistance_level + breakout_confirmation_margin
    ):
        # Additional filter: overall trend should be bullish (MA)
        if ma_trend == "BULLISH":
            print(
                f"BUY Signal! Resistance broken after {new_resistance_touch_count} touches. MA Trend: {ma_trend}"
            )
            # When giving buy signal, reset resistance counter to wait for new cycle
            new_resistance_touch_count = 0
            new_support_touch_count = 0  # Also reset support (to start fresh)
            return "BUY", new_resistance_touch_count, new_support_touch_count

    # --- Sell signal logic ---
    # If support touch count is 2 (or more), and it was broken downward in the last candle
    if (
        new_support_touch_count >= 2
        and last_candle["close"] < support_level - breakout_confirmation_margin
    ):
        # Additional filter: overall trend should be bearish (MA)
        if ma_trend == "BEARISH":
            print(
                f"SELL Signal! Support broken after {new_support_touch_count} touches. MA Trend: {ma_trend}"
            )
            # When giving sell signal, reset support counter
            new_support_touch_count = 0
            new_resistance_touch_count = 0  # Also reset resistance
            return "SELL", new_resistance_touch_count, new_support_touch_count

    return "NO_SIGNAL", new_resistance_touch_count, new_support_touch_count


# --- إدارة المراكز المفتوحة (Positions) ---
def manage_positions(symbol, trade_signal, trade_volume, current_open_position_type):
    """
    Manage open positions: close opposite positions and open new ones based on signal.
    :param symbol: Financial instrument symbol.
    :param trade_signal: Signal received ('BUY', 'SELL', 'NO_SIGNAL').
    :param trade_volume: Required trading volume.
    :param current_open_position_type: Currently open position type ('BUY', 'SELL', None).
    :return: New open position type ('BUY', 'SELL', None).
    """

    positions = mt5.positions_get(symbol=symbol)

    # Update current open position type based on actual positions in platform
    # This part is important to ensure the bot knows what position is actually open
    actual_position_type = None
    if positions:
        for pos in positions:
            if pos.type == mt5.ORDER_TYPE_BUY:
                actual_position_type = "BUY"
            elif pos.type == mt5.ORDER_TYPE_SELL:
                actual_position_type = "SELL"
            # If there's more than one position, this logic will choose the last one found
            # For more complex cases with multiple positions, we need more precise logic

    # If no position is currently open in the platform
    if not actual_position_type:
        if trade_signal == "BUY":
            print("No open position, BUY signal received. Placing BUY order.")
            place_trade_order(
                symbol, mt5.ORDER_TYPE_BUY, trade_volume, SLIPPAGE, MAGIC_NUMBER
            )
            return "BUY"
        elif trade_signal == "SELL":
            print("No open position, SELL signal received. Placing SELL order.")
            place_trade_order(
                symbol, mt5.ORDER_TYPE_SELL, trade_volume, SLIPPAGE, MAGIC_NUMBER
            )
            return "SELL"
        return None  # No position after this cycle

    # If there's an open position
    for position in positions:  # Process each open position

        # If signal is opposite to open position
        if (trade_signal == "BUY" and actual_position_type == "SELL") or (
            trade_signal == "SELL" and actual_position_type == "BUY"
        ):
            print(
                f"Opposite signal ({trade_signal}) received. Closing current {actual_position_type} position."
            )

            # Determine correct closing price
            current_tick = get_current_prices(symbol)
            if current_tick is None:
                print(
                    f"Could not get current tick for {symbol} to close position {position.ticket}."
                )
                return actual_position_type  # Don't change position state if we can't close

            close_price = (
                current_tick.bid
                if position.type == mt5.ORDER_TYPE_BUY
                else current_tick.ask
            )

            close_request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": position.symbol,
                "volume": position.volume,
                "type": (
                    mt5.ORDER_TYPE_SELL
                    if position.type == mt5.ORDER_TYPE_BUY
                    else mt5.ORDER_TYPE_BUY
                ),
                "position": position.ticket,
                "price": close_price,
                "deviation": SLIPPAGE,
                "magic": MAGIC_NUMBER,
                "comment": "Close_Bot_Position",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_RETURN,  # Allows partial execution
            }
            close_result = mt5.order_send(close_request)

            if close_result and (
                close_result.retcode == 10009 or close_result.retcode == 10008
            ):
                print(f"Position {position.ticket} closed successfully.")
                # After closing, try to open new position
                if trade_signal == "BUY":
                    place_trade_order(
                        symbol, mt5.ORDER_TYPE_BUY, trade_volume, SLIPPAGE, MAGIC_NUMBER
                    )
                    return "BUY"
                elif trade_signal == "SELL":
                    place_trade_order(
                        symbol,
                        mt5.ORDER_TYPE_SELL,
                        trade_volume,
                        SLIPPAGE,
                        MAGIC_NUMBER,
                    )
                    return "SELL"
                return None  # Closed but no new trade opened successfully
            else:
                print(
                    f"Failed to close position {position.ticket}. Error code: {close_result.retcode if close_result else mt5.last_error()}"
                )
                return actual_position_type  # Return same position state if closing failed

        # If signal matches open position direction (do nothing)
        elif (trade_signal == "BUY" and actual_position_type == "BUY") or (
            trade_signal == "SELL" and actual_position_type == "SELL"
        ):
            print(
                f"Signal ({trade_signal}) matches current open position. No action needed."
            )
            return actual_position_type  # Return same position state

        # If no signal and there are open trades, do nothing (keep position)
        elif trade_signal == "NO_SIGNAL":
            print("No signal, keeping current position open.")
            return actual_position_type

    return actual_position_type  # Return final state
