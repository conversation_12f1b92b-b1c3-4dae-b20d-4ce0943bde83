# نظام حساب الدعم والمقاومة التلقائي

## 🎯 التحديث الجديد

تم تطوير النظام ليحسب مستويات الدعم والمقاومة **تلقائياً** بدلاً من الاعتماد على القيم الثابتة.

## 🔧 الطرق المستخدمة

### 1. **Pivot Points (نقاط البيفوت)**
- **المبدأ**: يستخدم بيانات اليوم السابق (High, Low, Close)
- **المعادلات**:
  - `Pivot = (High + Low + Close) / 3`
  - `Resistance = 2 × Pivot - Low`
  - `Support = 2 × Pivot - High`
- **الوزن**: 60% من الحساب النهائي

### 2. **Swing Levels (مستويات التأرجح)**
- **المبدأ**: يجد أعلى وأقل النقاط في فترة معينة
- **القوة**: يتطلب 3 شموع على كل جانب لتأكيد النقطة
- **النطاق**: آخر 20 شمعة
- **الوزن**: 40% من الحساب النهائي

## 📁 الملفات المحدثة

### 1. **config.py**
```python
# إعدادات جديدة للحساب التلقائي
PIVOT_TIMEFRAME = mt5.TIMEFRAME_D1  # إطار زمني للبيفوت
SWING_LOOKBACK_PERIODS = 20         # عدد الشموع للبحث
SWING_STRENGTH = 3                  # قوة النقطة
PIVOT_WEIGHT = 0.6                  # وزن البيفوت
SWING_WEIGHT = 0.4                  # وزن السوينغ
```

### 2. **support_resistance.py** (جديد)
- `calculate_pivot_points()`: حساب نقاط البيفوت
- `find_swing_levels()`: العثور على مستويات التأرجح
- `calculate_dynamic_support_resistance()`: الدالة الرئيسية

### 3. **strategy_logic.py**
- تم تعديل `check_for_trade_signal()` لاستقبال المستويات كمعاملات
- إزالة الاعتماد على القيم الثابتة

### 4. **main.py**
- إضافة حساب المستويات في كل دورة
- تمرير المستويات المحسوبة للاستراتيجية

## 🧪 اختبار النظام

```bash
python test_support_resistance.py
```

هذا الملف سيختبر:
- الاتصال بـ MT5
- جلب البيانات التاريخية
- حساب المستويات
- عرض النتائج والتحليل

## 📊 مثال على النتائج

```
🎯 مستويات محدثة: مقاومة=2347.50, دعم=2335.20
📊 طريقة الحساب: pivot_swing_combined

📈 نتائج الحساب:
   🔴 مستوى المقاومة: 2347.50
   🟢 مستوى الدعم: 2335.20
   💰 السعر الحالي: 2341.30
   🔧 طريقة الحساب: pivot_swing_combined

📊 بيانات Pivot Points:
   • نقطة البيفوت: 2341.25
   • مقاومة 1: 2346.80
   • دعم 1: 2336.70

🔄 بيانات Swing Levels:
   • عدد مستويات المقاومة: 3
   • عدد مستويات الدعم: 4
```

## ⚙️ آلية العمل

1. **في كل دورة تداول**:
   - حساب Pivot Points من البيانات اليومية
   - العثور على Swing Levels من البيانات الحالية
   - دمج النتائج بالأوزان المحددة

2. **اختيار أفضل مستوى**:
   - للمقاومة: أقرب مستوى أعلى من السعر الحالي
   - للدعم: أقرب مستوى أقل من السعر الحالي

3. **نظام الاحتياط**:
   - إذا فشل الحساب، يستخدم القيم الافتراضية
   - إذا لم تتوفر بيانات كافية، يستخدم Pivot فقط

## 🔄 طرق الحساب المختلفة

- **`pivot_swing_combined`**: دمج كامل (الأفضل)
- **`pivot_only`**: Pivot Points فقط
- **`swing_only`**: Swing Levels فقط
- **`default_fallback`**: القيم الافتراضية
- **`error_fallback`**: في حالة الخطأ

## 🎯 المزايا

1. **تكيف تلقائي** مع حركة السوق
2. **دقة أعلى** من القيم الثابتة
3. **مرونة** في التعديل والتطوير
4. **شفافية** في طريقة الحساب
5. **نظام احتياط** قوي

## 🚀 التشغيل

النظام يعمل تلقائياً مع البوت الأساسي:

```bash
python main.py
```

ستظهر رسائل مثل:
```
🎯 مستويات محدثة: مقاومة=2347.50, دعم=2335.20
📊 طريقة الحساب: pivot_swing_combined
```

## 📝 ملاحظات مهمة

- النظام يحتاج اتصال بـ MT5 وبيانات تاريخية
- يُنصح بمراقبة النتائج في البداية
- يمكن تعديل الأوزان والإعدادات في `config.py`
- النظام يحفظ طريقة الحساب المستخدمة للمراجعة
