# 🤖 نظام محاكاة البوت (Backtesting)

## 🎯 الهدف

بدلاً من انتظار ساعات لرؤية نتائج البوت، يمكنك الآن اختباره على البيانات التاريخية في دقائق!

## 📁 ملفات المحاكاة

### 1. **simulation.py** - المحرك الأساسي
- `TradingSimulation`: كلاس المحاكاة الرئيسي
- حساب الأرباح والخسائر
- تتبع الصفقات والإحصائيات
- حساب أقصى انخفاض (Max Drawdown)

### 2. **run_simulation.py** - قائمة تفاعلية
- اختبارات متنوعة (أسبوع، شهر، مخصص)
- مقارنة إعدادات مختلفة
- واجهة سهلة الاستخدام

### 3. **quick_simulation.py** - اختبار سريع
- اختبار على آخر 3 أيام
- قياس الأداء والسرعة
- نتائج فورية

## 🚀 طرق التشغيل

### 1. **اختبار سريع (30 ثانية)**
```bash
python quick_simulation.py
```

### 2. **قائمة تفاعلية**
```bash
python run_simulation.py
```

### 3. **استخدام مباشر**
```python
from simulation import TradingSimulation
from datetime import datetime, timedelta

# إنشاء محاكي
sim = TradingSimulation(initial_balance=10000, lot_size=0.01)

# تحديد الفترة
end_date = datetime.now()
start_date = end_date - timedelta(days=7)

# تشغيل المحاكاة
sim.run_simulation(start_date, end_date)
sim.print_results()
```

## 📊 مثال على النتائج

```
🚀 بدء المحاكاة من 2024-01-15 إلى 2024-01-22
💰 الرصيد الابتدائي: 10000.00$
📊 حجم اللوت: 0.01
------------------------------------------------------------
📊 تم جلب 2000 شمعة للمحاكاة
📈 محاكاة على 672 شمعة
------------------------------------------------------------
📈 فتح صفقة BUY عند 2045.67 في 2024-01-16 09:15:00
✅ ربح: 156.80$ (15.7 نقطة)
💰 الرصيد الحالي: 10156.80$

📈 فتح صفقة SELL عند 2052.34 في 2024-01-17 14:30:00
❌ خسارة: -89.20$ (-8.9 نقطة)
💰 الرصيد الحالي: 10067.60$

============================================================
📊 نتائج المحاكاة
============================================================
💰 الرصيد الابتدائي: 10000.00$
💰 الرصيد النهائي: 10234.50$
📈 إجمالي الربح/الخسارة: +234.50$ (+2.35%)
📉 أقصى انخفاض: 1.23%

📊 إحصائيات الصفقات:
   • إجمالي الصفقات: 8
   • الصفقات الرابحة: 5
   • الصفقات الخاسرة: 3
   • معدل النجاح: 62.5%
   • متوسط الربح/الخسارة: 29.31$
```

## ⚙️ إعدادات المحاكاة

### المعاملات الأساسية:
- **initial_balance**: الرصيد الابتدائي (افتراضي: 10,000$)
- **lot_size**: حجم اللوت (افتراضي: 0.01)
- **spread**: السبريد بالنقاط (افتراضي: 0.5)

### فترات الاختبار:
- **اختبار سريع**: آخر 3 أيام
- **اختبار أسبوعي**: آخر 7 أيام
- **اختبار شهري**: آخر 30 يوم
- **اختبار مخصص**: أي فترة تريدها

## 📈 المؤشرات المحسوبة

### 1. **الربحية**
- إجمالي الربح/الخسارة ($)
- نسبة الربح (%)
- متوسط الربح لكل صفقة

### 2. **معدل النجاح**
- عدد الصفقات الرابحة
- عدد الصفقات الخاسرة
- نسبة النجاح (%)

### 3. **إدارة المخاطر**
- أقصى انخفاض (Max Drawdown)
- أعلى رصيد محقق
- تتبع المخاطر

## 🔧 المزايا

### ✅ **السرعة**
- اختبار أسبوع كامل في أقل من دقيقة
- لا حاجة لانتظار ساعات

### ✅ **الدقة**
- يستخدم نفس منطق البوت الحقيقي
- حساب دقيق للأرباح والخسائر
- تطبيق السبريد والعمولات

### ✅ **التنوع**
- اختبار فترات مختلفة
- مقارنة إعدادات متنوعة
- تحليل شامل للأداء

### ✅ **سهولة الاستخدام**
- واجهة تفاعلية بسيطة
- نتائج واضحة ومفصلة
- تشغيل بأمر واحد

## 🎯 حالات الاستخدام

### 1. **اختبار سريع قبل التشغيل**
```bash
python quick_simulation.py
```

### 2. **تحسين الإعدادات**
- اختبار أحجام لوت مختلفة
- مقارنة فترات زمنية
- تحليل الأداء

### 3. **تقييم الاستراتيجية**
- معدل النجاح
- إدارة المخاطر
- الربحية طويلة المدى

## ⚠️ ملاحظات مهمة

1. **البيانات التاريخية**: النتائج مبنية على بيانات الماضي
2. **السبريد**: يتم تطبيق سبريد افتراضي 0.5 نقطة
3. **الانزلاق**: لا يتم حساب الانزلاق في المحاكاة
4. **الأخبار**: فلترة الأخبار تعمل حسب منطق البوت

## 🚀 البدء السريع

1. **تأكد من تشغيل MT5**
2. **شغل الاختبار السريع**:
   ```bash
   python quick_simulation.py
   ```
3. **راجع النتائج**
4. **إذا كانت إيجابية، شغل البوت الحقيقي**:
   ```bash
   python main.py
   ```

## 💡 نصائح

- ابدأ بالاختبار السريع دائماً
- اختبر فترات مختلفة للتأكد من الثبات
- راقب أقصى انخفاض (يجب أن يكون < 10%)
- معدل نجاح > 50% مؤشر جيد
- لا تعتمد على نتيجة يوم واحد فقط
