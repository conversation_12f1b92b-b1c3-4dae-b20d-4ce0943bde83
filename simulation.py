# simulation.py
# نظام محاكاة البوت على البيانات التاريخية

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

try:
    from .config import SYMBOL, TIMEFRAME_TO_USE
    from .mt5_interface import initialize_mt5_connection, shutdown_mt5_connection, get_historical_data
    from .strategy_logic import check_for_trade_signal
    from .support_resistance import calculate_dynamic_support_resistance
except ImportError:
    from config import SYMBOL, TIMEFRAME_TO_USE
    from mt5_interface import initialize_mt5_connection, shutdown_mt5_connection, get_historical_data
    from strategy_logic import check_for_trade_signal
    from support_resistance import calculate_dynamic_support_resistance


class TradingSimulation:
    def __init__(self, initial_balance=10000, lot_size=0.01, spread=0.5):
        """
        Initialize trading simulator
        :param initial_balance: Initial balance
        :param lot_size: Lot size
        :param spread: Spread (in points)
        """
        self.initial_balance = initial_balance
        self.balance = initial_balance
        self.lot_size = lot_size
        self.spread = spread

        # Track trades
        self.trades = []
        self.open_position = None
        self.resistance_touch_count = 0
        self.support_touch_count = 0

        # Statistics
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.max_drawdown = 0
        self.peak_balance = initial_balance
        
    def calculate_profit_loss(self, entry_price, exit_price, trade_type, volume):
        """
        Calculate profit/loss for the trade
        """
        if trade_type == "BUY":
            pips = (exit_price - entry_price) * 10000  # Convert to points
        else:  # SELL
            pips = (entry_price - exit_price) * 10000

        # Calculate point value (approximate for gold)
        pip_value = volume * 100  # Point value for gold
        profit = pips * pip_value

        return profit, pips
    
    def open_trade(self, trade_type, price, timestamp):
        """
        Open new trade
        """
        if self.open_position is None:
            # Apply spread
            if trade_type == "BUY":
                entry_price = price + (self.spread / 10000)
            else:
                entry_price = price - (self.spread / 10000)

            self.open_position = {
                'type': trade_type,
                'entry_price': entry_price,
                'entry_time': timestamp,
                'volume': self.lot_size
            }
            print(f"📈 Opening {trade_type} trade at {entry_price:.2f} at {timestamp}")
            return True
        return False
    
    def close_trade(self, price, timestamp, reason="Signal"):
        """
        Close open trade
        """
        if self.open_position is not None:
            # Apply spread
            if self.open_position['type'] == "BUY":
                exit_price = price - (self.spread / 10000)
            else:
                exit_price = price + (self.spread / 10000)

            profit, pips = self.calculate_profit_loss(
                self.open_position['entry_price'],
                exit_price,
                self.open_position['type'],
                self.open_position['volume']
            )

            # Update balance
            self.balance += profit

            # Save trade
            trade_record = {
                'entry_time': self.open_position['entry_time'],
                'exit_time': timestamp,
                'type': self.open_position['type'],
                'entry_price': self.open_position['entry_price'],
                'exit_price': exit_price,
                'volume': self.open_position['volume'],
                'profit': profit,
                'pips': pips,
                'reason': reason
            }

            self.trades.append(trade_record)
            self.total_trades += 1

            if profit > 0:
                self.winning_trades += 1
                print(f"✅ Profit: {profit:.2f}$ ({pips:.1f} pips)")
            else:
                self.losing_trades += 1
                print(f"❌ Loss: {profit:.2f}$ ({pips:.1f} pips)")

            # Update max drawdown
            if self.balance > self.peak_balance:
                self.peak_balance = self.balance

            drawdown = (self.peak_balance - self.balance) / self.peak_balance * 100
            if drawdown > self.max_drawdown:
                self.max_drawdown = drawdown

            print(f"💰 Current balance: {self.balance:.2f}$")

            self.open_position = None
            return True
        return False
    
    def run_simulation(self, start_date, end_date, candles_per_step=200):
        """
        Run simulation on specified time period
        """
        print(f"🚀 Starting simulation from {start_date} to {end_date}")
        print(f"💰 Initial balance: {self.initial_balance}$")
        print(f"📊 Lot size: {self.lot_size}")
        print("-" * 60)

        # Initialize connection
        if not initialize_mt5_connection():
            print("❌ Failed to connect to MT5")
            return False
        
        try:
            # Get historical data
            total_candles = 2000  # Large number of candles for simulation
            rates = get_historical_data(SYMBOL, TIMEFRAME_TO_USE, total_candles)

            if rates is None or rates.empty:
                print("❌ Failed to get historical data")
                return False

            print(f"📊 Retrieved {len(rates)} candles for simulation")

            # Filter data by date
            rates['time'] = pd.to_datetime(rates['time'])
            mask = (rates['time'] >= start_date) & (rates['time'] <= end_date)
            simulation_data = rates.loc[mask].copy()

            if simulation_data.empty:
                print("❌ No data in specified period")
                return False

            print(f"📈 Simulating on {len(simulation_data)} candles")
            print("-" * 60)
            
            # Run simulation
            for i in range(candles_per_step, len(simulation_data)):
                current_candle = simulation_data.iloc[i]
                current_time = current_candle['time']
                current_price = current_candle['close']

                # Get data for analysis (last candles_per_step candles)
                analysis_data = simulation_data.iloc[i-candles_per_step:i].copy()

                # Calculate support and resistance levels
                sr_levels = calculate_dynamic_support_resistance(SYMBOL, analysis_data)
                resistance_level = sr_levels['resistance_level']
                support_level = sr_levels['support_level']

                # Check signals
                signal, self.resistance_touch_count, self.support_touch_count = check_for_trade_signal(
                    analysis_data,
                    current_time,
                    self.resistance_touch_count,
                    self.support_touch_count,
                    0.0001,  # Point value for gold
                    resistance_level,
                    support_level
                )

                # Execute trades
                if signal == "BUY":
                    if self.open_position and self.open_position['type'] == "SELL":
                        self.close_trade(current_price, current_time, "Opposite Signal")
                    if not self.open_position:
                        self.open_trade("BUY", current_price, current_time)

                elif signal == "SELL":
                    if self.open_position and self.open_position['type'] == "BUY":
                        self.close_trade(current_price, current_time, "Opposite Signal")
                    if not self.open_position:
                        self.open_trade("SELL", current_price, current_time)

            # Close any open trade at the end
            if self.open_position:
                final_price = simulation_data.iloc[-1]['close']
                final_time = simulation_data.iloc[-1]['time']
                self.close_trade(final_price, final_time, "End of Simulation")
            
            return True
            
        finally:
            shutdown_mt5_connection()
    
    def print_results(self):
        """
        Print simulation results
        """
        print("\n" + "="*60)
        print("📊 Simulation Results")
        print("="*60)

        total_profit = self.balance - self.initial_balance
        profit_percentage = (total_profit / self.initial_balance) * 100

        print(f"💰 Initial balance: {self.initial_balance:.2f}$")
        print(f"💰 Final balance: {self.balance:.2f}$")
        print(f"📈 Total profit/loss: {total_profit:.2f}$ ({profit_percentage:+.2f}%)")
        print(f"📉 Max drawdown: {self.max_drawdown:.2f}%")

        if self.total_trades > 0:
            win_rate = (self.winning_trades / self.total_trades) * 100
            print(f"\n📊 Trade statistics:")
            print(f"   • Total trades: {self.total_trades}")
            print(f"   • Winning trades: {self.winning_trades}")
            print(f"   • Losing trades: {self.losing_trades}")
            print(f"   • Win rate: {win_rate:.1f}%")

            if self.trades:
                profits = [t['profit'] for t in self.trades]
                avg_profit = np.mean(profits)
                print(f"   • Average profit/loss: {avg_profit:.2f}$")
        
        return {
            'total_profit': total_profit,
            'profit_percentage': profit_percentage,
            'total_trades': self.total_trades,
            'win_rate': win_rate if self.total_trades > 0 else 0,
            'max_drawdown': self.max_drawdown
        }
