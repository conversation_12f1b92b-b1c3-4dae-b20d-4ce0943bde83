# simulation.py
# نظام محاكاة البوت على البيانات التاريخية

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

try:
    from .config import (
        SYMBOL,
        TIMEFRAME_TO_USE,
        TOUCH_TOLERANCE_POINTS,
        BREAKOUT_CONFIRMATION_POINTS,
    )
    from .mt5_interface import initialize_mt5_connection, shutdown_mt5_connection, get_historical_data
    from .strategy_logic import check_for_trade_signal
    from .support_resistance import calculate_dynamic_support_resistance
except ImportError:
    from config import (
        SYMBOL,
        TIMEFRAME_TO_USE,
        TOUCH_TOLERANCE_POINTS,
        BREAKOUT_CONFIRMATION_POINTS,
    )
    from mt5_interface import initialize_mt5_connection, shutdown_mt5_connection, get_historical_data
    from strategy_logic import check_for_trade_signal
    from support_resistance import calculate_dynamic_support_resistance


class TradingSimulation:
    def __init__(self, initial_balance=10000, lot_size=0.01, spread=0.5):
        """
        تهيئة محاكي التداول
        :param initial_balance: الرصيد الابتدائي
        :param lot_size: حجم اللوت
        :param spread: السبريد (بالنقاط)
        """
        self.initial_balance = initial_balance
        self.balance = initial_balance
        self.lot_size = lot_size
        self.spread = spread
        
        # تتبع الصفقات
        self.trades = []
        self.open_position = None
        self.resistance_touch_count = 0
        self.support_touch_count = 0
        
        # إحصائيات
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.max_drawdown = 0
        self.peak_balance = initial_balance
        
    def calculate_profit_loss(self, entry_price, exit_price, trade_type, volume):
        """
        حساب الربح/الخسارة للصفقة
        """
        if trade_type == "BUY":
            pips = (exit_price - entry_price) * 10000  # تحويل لنقاط
        else:  # SELL
            pips = (entry_price - exit_price) * 10000
        
        # حساب قيمة النقطة (تقريبي للذهب)
        pip_value = volume * 100  # قيمة النقطة للذهب
        profit = pips * pip_value
        
        return profit, pips
    
    def open_trade(self, trade_type, price, timestamp):
        """
        فتح صفقة جديدة
        """
        if self.open_position is None:
            # تطبيق السبريد
            if trade_type == "BUY":
                entry_price = price + (self.spread / 10000)
            else:
                entry_price = price - (self.spread / 10000)
            
            self.open_position = {
                'type': trade_type,
                'entry_price': entry_price,
                'entry_time': timestamp,
                'volume': self.lot_size
            }
            print(f"📈 فتح صفقة {trade_type} عند {entry_price:.2f} في {timestamp}")
            return True
        return False
    
    def close_trade(self, price, timestamp, reason="Signal"):
        """
        إغلاق الصفقة المفتوحة
        """
        if self.open_position is not None:
            # تطبيق السبريد
            if self.open_position['type'] == "BUY":
                exit_price = price - (self.spread / 10000)
            else:
                exit_price = price + (self.spread / 10000)
            
            profit, pips = self.calculate_profit_loss(
                self.open_position['entry_price'],
                exit_price,
                self.open_position['type'],
                self.open_position['volume']
            )
            
            # تحديث الرصيد
            self.balance += profit
            
            # حفظ الصفقة
            trade_record = {
                'entry_time': self.open_position['entry_time'],
                'exit_time': timestamp,
                'type': self.open_position['type'],
                'entry_price': self.open_position['entry_price'],
                'exit_price': exit_price,
                'volume': self.open_position['volume'],
                'profit': profit,
                'pips': pips,
                'reason': reason
            }
            
            self.trades.append(trade_record)
            self.total_trades += 1
            
            if profit > 0:
                self.winning_trades += 1
                print(f"✅ ربح: {profit:.2f}$ ({pips:.1f} نقطة)")
            else:
                self.losing_trades += 1
                print(f"❌ خسارة: {profit:.2f}$ ({pips:.1f} نقطة)")
            
            # تحديث أقصى انخفاض
            if self.balance > self.peak_balance:
                self.peak_balance = self.balance
            
            drawdown = (self.peak_balance - self.balance) / self.peak_balance * 100
            if drawdown > self.max_drawdown:
                self.max_drawdown = drawdown
            
            print(f"💰 الرصيد الحالي: {self.balance:.2f}$")
            
            self.open_position = None
            return True
        return False
    
    def run_simulation(self, start_date, end_date, candles_per_step=200):
        """
        تشغيل المحاكاة على فترة زمنية محددة
        """
        print(f"🚀 بدء المحاكاة من {start_date} إلى {end_date}")
        print(f"💰 الرصيد الابتدائي: {self.initial_balance}$")
        print(f"📊 حجم اللوت: {self.lot_size}")
        print("-" * 60)
        
        # تهيئة الاتصال
        if not initialize_mt5_connection():
            print("❌ فشل في الاتصال بـ MT5")
            return False
        
        try:
            # جلب البيانات التاريخية
            total_candles = 2000  # عدد كبير من الشموع للمحاكاة
            rates = get_historical_data(SYMBOL, TIMEFRAME_TO_USE, total_candles)
            
            if rates is None or rates.empty:
                print("❌ فشل في جلب البيانات التاريخية")
                return False
            
            print(f"📊 تم جلب {len(rates)} شمعة للمحاكاة")
            
            # تصفية البيانات حسب التاريخ
            rates['time'] = pd.to_datetime(rates['time'])
            mask = (rates['time'] >= start_date) & (rates['time'] <= end_date)
            simulation_data = rates.loc[mask].copy()
            
            if simulation_data.empty:
                print("❌ لا توجد بيانات في الفترة المحددة")
                return False
            
            print(f"📈 محاكاة على {len(simulation_data)} شمعة")
            print("-" * 60)
            
            # تشغيل المحاكاة
            for i in range(candles_per_step, len(simulation_data)):
                current_candle = simulation_data.iloc[i]
                current_time = current_candle['time']
                current_price = current_candle['close']
                
                # جلب البيانات للتحليل (آخر candles_per_step شمعة)
                analysis_data = simulation_data.iloc[i-candles_per_step:i].copy()
                
                # حساب مستويات الدعم والمقاومة
                sr_levels = calculate_dynamic_support_resistance(SYMBOL, analysis_data)
                resistance_level = sr_levels['resistance_level']
                support_level = sr_levels['support_level']
                
                # فحص الإشارات
                signal, self.resistance_touch_count, self.support_touch_count = check_for_trade_signal(
                    analysis_data,
                    current_time,
                    self.resistance_touch_count,
                    self.support_touch_count,
                    0.0001,  # قيمة النقطة للذهب
                    resistance_level,
                    support_level
                )
                
                # تنفيذ الصفقات
                if signal == "BUY":
                    if self.open_position and self.open_position['type'] == "SELL":
                        self.close_trade(current_price, current_time, "Opposite Signal")
                    if not self.open_position:
                        self.open_trade("BUY", current_price, current_time)
                
                elif signal == "SELL":
                    if self.open_position and self.open_position['type'] == "BUY":
                        self.close_trade(current_price, current_time, "Opposite Signal")
                    if not self.open_position:
                        self.open_trade("SELL", current_price, current_time)
            
            # إغلاق أي صفقة مفتوحة في النهاية
            if self.open_position:
                final_price = simulation_data.iloc[-1]['close']
                final_time = simulation_data.iloc[-1]['time']
                self.close_trade(final_price, final_time, "End of Simulation")
            
            return True
            
        finally:
            shutdown_mt5_connection()
    
    def print_results(self):
        """
        طباعة نتائج المحاكاة
        """
        print("\n" + "="*60)
        print("📊 نتائج المحاكاة")
        print("="*60)
        
        total_profit = self.balance - self.initial_balance
        profit_percentage = (total_profit / self.initial_balance) * 100
        
        print(f"💰 الرصيد الابتدائي: {self.initial_balance:.2f}$")
        print(f"💰 الرصيد النهائي: {self.balance:.2f}$")
        print(f"📈 إجمالي الربح/الخسارة: {total_profit:.2f}$ ({profit_percentage:+.2f}%)")
        print(f"📉 أقصى انخفاض: {self.max_drawdown:.2f}%")
        
        if self.total_trades > 0:
            win_rate = (self.winning_trades / self.total_trades) * 100
            print(f"\n📊 إحصائيات الصفقات:")
            print(f"   • إجمالي الصفقات: {self.total_trades}")
            print(f"   • الصفقات الرابحة: {self.winning_trades}")
            print(f"   • الصفقات الخاسرة: {self.losing_trades}")
            print(f"   • معدل النجاح: {win_rate:.1f}%")
            
            if self.trades:
                profits = [t['profit'] for t in self.trades]
                avg_profit = np.mean(profits)
                print(f"   • متوسط الربح/الخسارة: {avg_profit:.2f}$")
        
        return {
            'total_profit': total_profit,
            'profit_percentage': profit_percentage,
            'total_trades': self.total_trades,
            'win_rate': win_rate if self.total_trades > 0 else 0,
            'max_drawdown': self.max_drawdown
        }
