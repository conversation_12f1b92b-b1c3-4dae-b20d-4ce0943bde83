# simulation.py
# Advanced simulation with multi-indicator strategy

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

try:
    from .config import SYMB<PERSON>, TIMEFRAME_TO_USE
    from .mt5_interface import initialize_mt5_connection, shutdown_mt5_connection, get_historical_data
    from .support_resistance import calculate_dynamic_support_resistance
    from .strategy import check_advanced_trade_signal, get_risk_management_params
    from .technical_indicators import get_all_indicators, analyze_trend_strength, get_individual_signals
except ImportError:
    from config import SYMBOL, TIMEFRAME_TO_USE
    from mt5_interface import initialize_mt5_connection, shutdown_mt5_connection, get_historical_data
    from support_resistance import calculate_dynamic_support_resistance
    from strategy import check_advanced_trade_signal, get_risk_management_params
    from technical_indicators import get_all_indicators, analyze_trend_strength, get_individual_signals


class AdvancedTradingSimulation:
    def __init__(self, initial_balance=10000, base_lot_size=0.01, spread=0.5, use_stop_loss=True):
        """
        Initialize advanced trading simulator
        """
        self.initial_balance = initial_balance
        self.balance = initial_balance
        self.base_lot_size = base_lot_size
        self.spread = spread
        self.use_stop_loss = use_stop_loss
        self.stop_loss_percentage = 0.005  # 0.5% dynamic stop loss
        
        # Track trades with more details
        self.trades = []

        # Multiple positions tracking
        self.open_positions = []  # List of open positions
        self.max_positions = 5    # Maximum simultaneous positions
        self.position_id_counter = 0
        self.open_position = None  # Keep for backward compatibility
        
        # Enhanced statistics
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.max_drawdown = 0
        self.peak_balance = initial_balance
        self.confidence_scores = []
        self.trend_analysis = []

        # Entry timing enhancement (disabled for more trades)
        self.pending_signal = None
        self.signal_confirmation_required = False  # Disabled for aggressive trading
        
    def calculate_profit_loss(self, entry_price, exit_price, trade_type, volume):
        """
        Calculate profit/loss for the trade (corrected for Gold/XAUUSDm)
        """
        # Calculate price difference
        if trade_type == "BUY":
            price_diff = exit_price - entry_price
        else:  # SELL
            price_diff = entry_price - exit_price

        # For Gold (XAUUSDm): 1 lot = 100 oz, price is per oz
        # Profit = price_difference * contract_size * volume
        contract_size = 100  # 100 oz per lot for gold
        profit = price_diff * contract_size * volume

        # Calculate pips for display (1 pip = 0.01 for gold)
        pips = price_diff / 0.01

        return profit, pips
    
    def open_trade(self, trade_type, price, timestamp, confidence, volume):
        """
        Open new trade with confidence tracking
        """
        if self.open_position is None:
            # Validate price
            if price <= 0 or price > 10000:  # Reasonable range for gold
                print(f"⚠️ WARNING: Invalid price {price:.2f}, skipping trade")
                return False

            # Apply spread (convert to price units)
            spread_in_price = self.spread * 0.01  # Convert points to price
            if trade_type == "BUY":
                entry_price = price + spread_in_price
            else:
                entry_price = price - spread_in_price

            # Calculate dynamic stop loss
            if self.use_stop_loss:
                if trade_type == "BUY":
                    stop_loss = entry_price * (1 - self.stop_loss_percentage)
                else:  # SELL
                    stop_loss = entry_price * (1 + self.stop_loss_percentage)
            else:
                stop_loss = None

            self.open_position = {
                'type': trade_type,
                'entry_price': entry_price,
                'entry_time': timestamp,
                'volume': volume,
                'confidence': confidence,
                'stop_loss': stop_loss
            }
            print(f"📈 Opening {trade_type} at {entry_price:.2f} (Vol: {volume}, Conf: {confidence:.1f}%)")
            return True
        return False

    def open_individual_trade(self, trade_type, price, timestamp, confidence, volume, rule_name):
        """
        Open individual trade based on specific rule (supports multiple positions)
        """
        # Check if we can open more positions
        if len(self.open_positions) >= self.max_positions:
            print(f"⚠️ Max positions ({self.max_positions}) reached. Skipping {trade_type} from {rule_name}")
            return False

        # Apply spread (convert to price units)
        spread_in_price = self.spread * 0.01
        if trade_type == "BUY":
            entry_price = price + spread_in_price
        else:
            entry_price = price - spread_in_price

        # Calculate dynamic stop loss and take profit
        if self.use_stop_loss:
            if trade_type == "BUY":
                stop_loss = entry_price * (1 - self.stop_loss_percentage)
                take_profit = entry_price * (1 + (self.stop_loss_percentage * 3))  # 3:1 risk-reward
            else:
                stop_loss = entry_price * (1 + self.stop_loss_percentage)
                take_profit = entry_price * (1 - (self.stop_loss_percentage * 3))  # 3:1 risk-reward
        else:
            stop_loss = None
            take_profit = None

        # Create new position with take profit
        self.position_id_counter += 1
        new_position = {
            'id': self.position_id_counter,
            'type': trade_type,
            'entry_price': entry_price,
            'entry_time': timestamp,
            'volume': volume,
            'confidence': confidence,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'rule_name': rule_name
        }

        self.open_positions.append(new_position)

        # Keep first position as main for backward compatibility
        if not self.open_position:
            self.open_position = new_position

        print(f"🚀 {trade_type} #{new_position['id']} opened at {entry_price:.2f} (Rule: {rule_name}, Conf: {confidence:.1f}%)")
        print(f"💼 Open positions: {len(self.open_positions)}")

        return True

    def close_individual_trade(self, position_id, price, timestamp, reason="Signal"):
        """
        Close specific individual trade
        """
        position_to_close = None
        for i, pos in enumerate(self.open_positions):
            if pos['id'] == position_id:
                position_to_close = pos
                break

        if not position_to_close:
            return False

        # Apply spread
        if position_to_close['type'] == "BUY":
            exit_price = price - (self.spread / 10000)
        else:
            exit_price = price + (self.spread / 10000)

        profit, pips = self.calculate_profit_loss(
            position_to_close['entry_price'],
            exit_price,
            position_to_close['type'],
            position_to_close['volume']
        )

        # Update balance
        self.balance += profit

        # Record trade
        trade_record = {
            'entry_time': position_to_close['entry_time'],
            'exit_time': timestamp,
            'type': position_to_close['type'],
            'entry_price': position_to_close['entry_price'],
            'exit_price': exit_price,
            'volume': position_to_close['volume'],
            'profit': profit,
            'pips': pips,
            'confidence': position_to_close['confidence'],
            'reason': reason,
            'rule_name': position_to_close['rule_name'],
            'position_id': position_to_close['id'],
            'duration': timestamp - position_to_close['entry_time']
        }

        self.trades.append(trade_record)
        self.total_trades += 1

        if profit > 0:
            self.winning_trades += 1
            print(f"✅ Position #{position_id} closed: +${profit:.2f} ({pips:.1f} pips) - {position_to_close['rule_name']}")
        else:
            self.losing_trades += 1
            print(f"❌ Position #{position_id} closed: ${profit:.2f} ({pips:.1f} pips) - {position_to_close['rule_name']}")

        # Remove from open positions
        self.open_positions = [pos for pos in self.open_positions if pos['id'] != position_id]

        # Update main position reference
        if self.open_position and self.open_position['id'] == position_id:
            self.open_position = self.open_positions[0] if self.open_positions else None

        print(f"💰 Balance: ${self.balance:.2f}, Open positions: {len(self.open_positions)}")

        return True

    def close_trade(self, price, timestamp, reason="Signal"):
        """
        Close open trade with enhanced tracking
        """
        if self.open_position is not None:
            # Apply spread
            if self.open_position['type'] == "BUY":
                exit_price = price - (self.spread / 10000)
            else:
                exit_price = price + (self.spread / 10000)
            
            profit, pips = self.calculate_profit_loss(
                self.open_position['entry_price'],
                exit_price,
                self.open_position['type'],
                self.open_position['volume']
            )

            # Sanity check: prevent unrealistic profits
            max_reasonable_profit = self.initial_balance * 0.1  # Max 10% per trade
            if abs(profit) > max_reasonable_profit:
                print(f"⚠️ WARNING: Unrealistic profit detected: ${profit:.2f}")
                print(f"   Entry: {self.open_position['entry_price']:.2f}")
                print(f"   Exit: {exit_price:.2f}")
                print(f"   Volume: {self.open_position['volume']}")
                print(f"   Limiting profit to reasonable amount...")
                profit = max_reasonable_profit if profit > 0 else -max_reasonable_profit
                pips = profit / (self.open_position['volume'] * 100)

            # Update balance
            self.balance += profit
            
            # Enhanced trade record
            trade_record = {
                'entry_time': self.open_position['entry_time'],
                'exit_time': timestamp,
                'type': self.open_position['type'],
                'entry_price': self.open_position['entry_price'],
                'exit_price': exit_price,
                'volume': self.open_position['volume'],
                'profit': profit,
                'pips': pips,
                'confidence': self.open_position['confidence'],
                'reason': reason,
                'duration': timestamp - self.open_position['entry_time'],
                'stop_loss_used': self.open_position.get('stop_loss') is not None,
                'stop_loss_hit': reason == "Stop Loss Hit"
            }
            
            self.trades.append(trade_record)
            self.total_trades += 1
            self.confidence_scores.append(self.open_position['confidence'])
            
            if profit > 0:
                self.winning_trades += 1
                print(f"✅ Profit: ${profit:.2f} ({pips:.1f} pips) - Conf: {self.open_position['confidence']:.1f}%")
            else:
                self.losing_trades += 1
                print(f"❌ Loss: ${profit:.2f} ({pips:.1f} pips) - Conf: {self.open_position['confidence']:.1f}%")
            
            # Update max drawdown
            if self.balance > self.peak_balance:
                self.peak_balance = self.balance
            
            drawdown = (self.peak_balance - self.balance) / self.peak_balance * 100
            if drawdown > self.max_drawdown:
                self.max_drawdown = drawdown
            
            print(f"💰 Balance: ${self.balance:.2f}")
            
            self.open_position = None
            return True
        return False
    
    def run_advanced_simulation(self, start_date, end_date, candles_per_step=200):
        """
        Run simulation with advanced strategy
        """
        print(f"🚀 Starting Advanced Simulation: {start_date} to {end_date}")
        print(f"💰 Initial balance: ${self.initial_balance}")
        print(f"📊 Base lot size: {self.base_lot_size}")
        print("-" * 60)
        
        if not initialize_mt5_connection():
            print("❌ Failed to connect to MT5")
            return False
        
        try:
            # Get historical data
            total_candles = 2000
            rates = get_historical_data(SYMBOL, TIMEFRAME_TO_USE, total_candles)
            
            if rates is None or rates.empty:
                print("❌ Failed to get historical data")
                return False
            
            print(f"📊 Retrieved {len(rates)} candles")
            
            # Filter data by date
            rates['time'] = pd.to_datetime(rates['time'])
            mask = (rates['time'] >= start_date) & (rates['time'] <= end_date)
            simulation_data = rates.loc[mask].copy()
            
            if simulation_data.empty:
                print("❌ No data in specified period")
                return False
            
            print(f"📈 Simulating on {len(simulation_data)} candles")
            print("-" * 60)
            
            # Run simulation
            for i in range(candles_per_step, len(simulation_data)):
                current_candle = simulation_data.iloc[i]
                current_time = current_candle['time']
                current_price = current_candle['close']
                
                # Get analysis data
                analysis_data = simulation_data.iloc[i-candles_per_step:i].copy()
                
                # Calculate S/R levels
                sr_levels = calculate_dynamic_support_resistance(SYMBOL, analysis_data)
                resistance_level = sr_levels['resistance_level']
                support_level = sr_levels['support_level']
                
                # Get trend analysis for tracking
                indicators = get_all_indicators(analysis_data)
                if indicators:
                    trend = analyze_trend_strength(indicators)
                    self.trend_analysis.append(trend)
                
                # Check stop loss first
                if self.open_position and self.use_stop_loss and self.open_position['stop_loss']:
                    stop_loss = self.open_position['stop_loss']
                    if ((self.open_position['type'] == "BUY" and current_price <= stop_loss) or
                        (self.open_position['type'] == "SELL" and current_price >= stop_loss)):
                        self.close_trade(current_price, current_time, "Stop Loss Hit")
                        continue

                # Check advanced signals
                signal, confidence = check_advanced_trade_signal(
                    analysis_data, current_time, resistance_level, support_level, debug=False
                )
                
                # Simplified aggressive trade execution
                if signal == "BUY":
                    if self.open_position and self.open_position['type'] == "SELL":
                        self.close_trade(current_price, current_time, "Opposite Signal")
                    if not self.open_position:
                        volume = get_risk_management_params(confidence, self.base_lot_size)
                        self.open_trade("BUY", current_price, current_time, confidence, volume)
                        print(f"🚀 BUY executed immediately ({confidence:.1f}%)")

                elif signal == "SELL":
                    if self.open_position and self.open_position['type'] == "BUY":
                        self.close_trade(current_price, current_time, "Opposite Signal")
                    if not self.open_position:
                        volume = get_risk_management_params(confidence, self.base_lot_size)
                        self.open_trade("SELL", current_price, current_time, confidence, volume)
                        print(f"🚀 SELL executed immediately ({confidence:.1f}%)")
            
            # Close any remaining position
            if self.open_position:
                final_price = simulation_data.iloc[-1]['close']
                final_time = simulation_data.iloc[-1]['time']
                self.close_trade(final_price, final_time, "End of Simulation")
            
            return True
            
        finally:
            shutdown_mt5_connection()

    def run_individual_signals_simulation(self, start_date, end_date, candles_per_step=200):
        """
        Run simulation with individual signal triggers (any rule can trigger trade)
        """
        print(f"🚀 Starting Individual Signals Simulation: {start_date} to {end_date}")
        print(f"💰 Initial balance: ${self.initial_balance}")
        print(f"📊 Base lot size: {self.base_lot_size}")
        print(f"🔥 Max simultaneous positions: {self.max_positions}")
        print("-" * 60)

        if not initialize_mt5_connection():
            print("❌ Failed to connect to MT5")
            return False

        try:
            # Get historical data
            total_candles = 2000
            rates = get_historical_data(SYMBOL, TIMEFRAME_TO_USE, total_candles)

            if rates is None or rates.empty:
                print("❌ Failed to get historical data")
                return False

            print(f"📊 Retrieved {len(rates)} candles")

            # Filter data by date
            rates['time'] = pd.to_datetime(rates['time'])
            mask = (rates['time'] >= start_date) & (rates['time'] <= end_date)
            simulation_data = rates.loc[mask].copy()

            if simulation_data.empty:
                print("❌ No data in specified period")
                return False

            print(f"📈 Simulating on {len(simulation_data)} candles")
            print("-" * 60)

            # Run simulation
            for i in range(candles_per_step, len(simulation_data)):
                current_candle = simulation_data.iloc[i]
                current_time = current_candle['time']
                current_price = current_candle['close']

                # Get analysis data
                analysis_data = simulation_data.iloc[i-candles_per_step:i].copy()

                # Calculate S/R levels
                sr_levels = calculate_dynamic_support_resistance(SYMBOL, analysis_data)
                resistance_level = sr_levels['resistance_level']
                support_level = sr_levels['support_level']

                # Get indicators and trend
                indicators = get_all_indicators(analysis_data)
                if indicators:
                    trend = analyze_trend_strength(indicators)
                    self.trend_analysis.append(trend)

                    # Check stop loss AND take profit for all open positions
                    positions_to_close = []
                    for pos in self.open_positions:
                        # Check stop loss
                        if self.use_stop_loss and pos['stop_loss']:
                            if ((pos['type'] == "BUY" and current_price <= pos['stop_loss']) or
                                (pos['type'] == "SELL" and current_price >= pos['stop_loss'])):
                                positions_to_close.append((pos['id'], "Stop Loss Hit"))

                        # Check take profit
                        if pos.get('take_profit'):
                            if ((pos['type'] == "BUY" and current_price >= pos['take_profit']) or
                                (pos['type'] == "SELL" and current_price <= pos['take_profit'])):
                                positions_to_close.append((pos['id'], "Take Profit Hit"))

                    # Close positions that hit stop loss or take profit
                    for pos_id, reason in positions_to_close:
                        self.close_individual_trade(pos_id, current_price, current_time, reason)

                    # Get individual signals
                    individual_signals = get_individual_signals(
                        indicators, current_price, support_level, resistance_level, trend
                    )

                    # DUAL CONFIRMATION SYSTEM: Require minimum 2 rules agreement
                    buy_signals = []
                    sell_signals = []

                    # Quality filter for profitable rules
                    profitable_rules = [
                        "STRONG_BULL_TREND", "MOMENTUM_UP", "STRONG_MOMENTUM_UP",
                        "SUPPORT_BOUNCE", "BB_BOUNCE_BUY", "MACD_BULLISH_CROSS",
                        "STRONG_BEAR_TREND", "MOMENTUM_DOWN", "STRONG_MOMENTUM_DOWN",
                        "RESISTANCE_REJECT", "BB_BOUNCE_SELL", "MACD_BEARISH_CROSS",
                        "BREAKOUT_UP", "BREAKOUT_DOWN", "TRIPLE_BULL_CONFIRM", "TRIPLE_BEAR_CONFIRM",
                        "RSI_OVERSOLD", "RSI_OVERBOUGHT"
                    ]

                    # Group signals by direction
                    for rule_name, signal, confidence in individual_signals:
                        if confidence >= 70 and rule_name in profitable_rules:
                            if signal == "BUY":
                                buy_signals.append((rule_name, confidence))
                            elif signal == "SELL":
                                sell_signals.append((rule_name, confidence))

                    # Execute only with dual confirmation (2+ signals in same direction)
                    if len(buy_signals) >= 2 and len(self.open_positions) < self.max_positions:
                        # Check for conflicts
                        conflicting_positions = [pos for pos in self.open_positions if pos['type'] == "SELL"]

                        if not conflicting_positions:
                            # Calculate combined confidence (average but capped at 95)
                            combined_confidence = min(95, sum([conf for _, conf in buy_signals]) / len(buy_signals))
                            rule_names = [name for name, _ in buy_signals]
                            volume = get_risk_management_params(combined_confidence, self.base_lot_size)

                            success = self.open_individual_trade(
                                "BUY", current_price, current_time, combined_confidence, volume,
                                f"DUAL_CONFIRM_{'+'.join(rule_names[:2])}"
                            )
                            if success:
                                print(f"🎯 DUAL BUY CONFIRMED by {rule_names[:2]} (Conf: {combined_confidence:.1f}%)")

                    if len(sell_signals) >= 2 and len(self.open_positions) < self.max_positions:
                        # Check for conflicts
                        conflicting_positions = [pos for pos in self.open_positions if pos['type'] == "BUY"]

                        if not conflicting_positions:
                            # Calculate combined confidence (average but capped at 95)
                            combined_confidence = min(95, sum([conf for _, conf in sell_signals]) / len(sell_signals))
                            rule_names = [name for name, _ in sell_signals]
                            volume = get_risk_management_params(combined_confidence, self.base_lot_size)

                            success = self.open_individual_trade(
                                "SELL", current_price, current_time, combined_confidence, volume,
                                f"DUAL_CONFIRM_{'+'.join(rule_names[:2])}"
                            )
                            if success:
                                print(f"🎯 DUAL SELL CONFIRMED by {rule_names[:2]} (Conf: {combined_confidence:.1f}%)")

                    # Also check for opposite signals to close positions
                    for pos in self.open_positions.copy():  # Use copy to avoid modification during iteration
                        for rule_name, signal, confidence in individual_signals:
                            if confidence >= 60:  # Higher threshold for closing
                                if ((pos['type'] == "BUY" and signal == "SELL") or
                                    (pos['type'] == "SELL" and signal == "BUY")):
                                    self.close_individual_trade(
                                        pos['id'], current_price, current_time, f"Opposite Signal: {rule_name}"
                                    )
                                    break  # Only close once per position

            # Close all remaining positions
            for pos in self.open_positions.copy():
                final_price = simulation_data.iloc[-1]['close']
                final_time = simulation_data.iloc[-1]['time']
                self.close_individual_trade(pos['id'], final_price, final_time, "End of Simulation")

            return True

        finally:
            shutdown_mt5_connection()

    def print_advanced_results(self):
        """
        Print enhanced simulation results
        """
        print("\n" + "="*60)
        print("📊 Advanced Simulation Results")
        print("="*60)
        
        total_profit = self.balance - self.initial_balance
        profit_percentage = (total_profit / self.initial_balance) * 100
        
        print(f"💰 Initial balance: ${self.initial_balance:.2f}")
        print(f"💰 Final balance: ${self.balance:.2f}")
        print(f"📈 Total profit/loss: ${total_profit:.2f} ({profit_percentage:+.2f}%)")
        print(f"📉 Max drawdown: {self.max_drawdown:.2f}%")
        
        if self.total_trades > 0:
            win_rate = (self.winning_trades / self.total_trades) * 100
            avg_confidence = np.mean(self.confidence_scores)
            
            print(f"\n📊 Trade Statistics:")
            print(f"   • Total trades: {self.total_trades}")
            print(f"   • Winning trades: {self.winning_trades}")
            print(f"   • Losing trades: {self.losing_trades}")
            print(f"   • Win rate: {win_rate:.1f}%")
            print(f"   • Average confidence: {avg_confidence:.1f}%")
            
            if self.trades:
                profits = [t['profit'] for t in self.trades]
                avg_profit = np.mean(profits)
                best_trade = max(profits)
                worst_trade = min(profits)
                
                print(f"   • Average profit/loss: ${avg_profit:.2f}")
                print(f"   • Best trade: ${best_trade:.2f}")
                print(f"   • Worst trade: ${worst_trade:.2f}")
                
                # Confidence analysis
                winning_trades = [t for t in self.trades if t['profit'] > 0]
                losing_trades = [t for t in self.trades if t['profit'] <= 0]
                
                if winning_trades:
                    avg_win_confidence = np.mean([t['confidence'] for t in winning_trades])
                    print(f"   • Avg winning confidence: {avg_win_confidence:.1f}%")
                
                if losing_trades:
                    avg_loss_confidence = np.mean([t['confidence'] for t in losing_trades])
                    print(f"   • Avg losing confidence: {avg_loss_confidence:.1f}%")

                # Stop loss analysis
                stop_loss_trades = [t for t in self.trades if t.get('stop_loss_hit', False)]
                if stop_loss_trades:
                    stop_loss_rate = (len(stop_loss_trades) / self.total_trades) * 100
                    print(f"   • Stop loss hits: {len(stop_loss_trades)} ({stop_loss_rate:.1f}%)")

                # Trade timing analysis
                high_conf_trades = [t for t in self.trades if t['confidence'] >= 70]
                medium_conf_trades = [t for t in self.trades if 60 <= t['confidence'] < 70]

                if high_conf_trades:
                    high_conf_win_rate = (len([t for t in high_conf_trades if t['profit'] > 0]) / len(high_conf_trades)) * 100
                    print(f"   • High confidence win rate: {high_conf_win_rate:.1f}% ({len(high_conf_trades)} trades)")

                if medium_conf_trades:
                    medium_conf_win_rate = (len([t for t in medium_conf_trades if t['profit'] > 0]) / len(medium_conf_trades)) * 100
                    print(f"   • Medium confidence win rate: {medium_conf_win_rate:.1f}% ({len(medium_conf_trades)} trades)")
        
        # Trend analysis
        if self.trend_analysis:
            trend_counts = {}
            for trend in self.trend_analysis:
                trend_counts[trend] = trend_counts.get(trend, 0) + 1
            
            print(f"\n📈 Market Conditions:")
            for trend, count in trend_counts.items():
                percentage = (count / len(self.trend_analysis)) * 100
                print(f"   • {trend}: {percentage:.1f}%")
        
        return {
            'total_profit': total_profit,
            'profit_percentage': profit_percentage,
            'total_trades': self.total_trades,
            'win_rate': win_rate if self.total_trades > 0 else 0,
            'max_drawdown': self.max_drawdown,
            'avg_confidence': np.mean(self.confidence_scores) if self.confidence_scores else 0
        }
