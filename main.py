# main.py

import MetaTrader5 as mt5
import pandas as pd
import time

# استيراد الوحدات والوظائف من الملفات الأخرى
try:
    # محاولة الاستيراد النسبي (عند التشغيل كجزء من حزمة)
    from .config import (
        SYMBOL,
        TIMEFRAME_TO_USE,
        NUM_CANDLES_FOR_DATA,
        TOUCH_TOLERANCE_POINTS,
        BREAKOUT_CONFIRMATION_POINTS,
    )
    from .mt5_interface import (
        initialize_mt5_connection,
        shutdown_mt5_connection,
        get_account_details,
        get_historical_data,
    )
    from .strategy_logic import check_for_trade_signal, manage_positions
    from .support_resistance import calculate_dynamic_support_resistance
except ImportError:
    # الاستيراد المباشر (عند التشغيل كملف منفصل)
    from config import (
        SYMBOL,
        TIMEFRAME_TO_USE,
        NUM_CANDLES_FOR_DATA,
        TOUCH_TOLERANCE_POINTS,
        BREAKOUT_CONFIRMATION_POINTS,
    )
    from mt5_interface import (
        initialize_mt5_connection,
        shutdown_mt5_connection,
        get_account_details,
        get_historical_data,
    )
    from strategy_logic import check_for_trade_signal, manage_positions
    from support_resistance import calculate_dynamic_support_resistance

# --- المتغيرات الجلوبال للحفاظ على حالة البوت ---
# هذه المتغيرات ستحفظ حالتها بين دورات الحلقة الرئيسية
# سنقوم بتحديثها بناءً على المخرجات من وظائف strategy_logic
resistance_touch_count = 0
support_touch_count = 0
current_open_position_type = None  # 'BUY', 'SELL', أو None
last_trade_minute = -1  # لتخزين الدقيقة التي تم فيها آخر تداول (لمنع التكرار)

# --- الجزء الرئيسي لتشغيل السكريبت ---
if __name__ == "__main__":
    if not initialize_mt5_connection():
        exit()  # إذا فشل الاتصال، نخرج من السكريبت

    try:
        account_data = get_account_details()
        if not account_data or account_data.balance <= 0:
            print("Account balance is zero or not available. Cannot place trades.")
            # يمكنك اختيار exit() هنا إذا كنت لا تريد تشغيل البوت بدون رصيد
            # ولكن سنستمر للسماح بعرض البيانات حتى لو لم يكن هناك رصيد كافٍ للتداول

        # الحصول على معلومات الرمز لتحديد حجم اللوت الأدنى وقيمة النقطة
        symbol_info = mt5.symbol_info(SYMBOL)
        if not symbol_info:
            print(f"Failed to get symbol info for {SYMBOL}. Exiting.")
            shutdown_mt5_connection()
            exit()

        trade_volume = symbol_info.volume_min  # حجم التداول الأدنى المسموح به
        point_value = symbol_info.point  # قيمة النقطة للرمز

        # حساب هوامش الملامسة والكسر بناءً على قيمة النقطة
        TOUCH_TOLERANCE_VALUE = TOUCH_TOLERANCE_POINTS * point_value
        BREAKOUT_CONFIRMATION_VALUE = BREAKOUT_CONFIRMATION_POINTS * point_value

        print(
            f"\n--- Starting {str(TIMEFRAME_TO_USE).split('_')[-1]} Trading Loop for {SYMBOL} ---"
        )
        print(f"Minimum allowed volume for {SYMBOL}: {trade_volume}")
        print(f"Point value for {SYMBOL}: {point_value}")
        print(
            f"Touch Tolerance: {TOUCH_TOLERANCE_VALUE:.5f}, Breakout Confirmation Margin: {BREAKOUT_CONFIRMATION_VALUE:.5f}"
        )

        # حلقة لا نهائية لتشغيل البوت
        while True:
            current_time = pd.to_datetime(mt5.time_srv(), unit="s")

            # منطق المزامنة مع الشمعة (لإطار M15)
            # ننتظر حتى مرور بضع ثوانٍ من بداية ربع الساعة (0, 15, 30, 45)
            # للتأكد من أن الشمعة السابقة قد أغلقت بالكامل
            if current_time.minute % 15 == 0 and current_time.second < 10:
                print(
                    f"\n--- Checking signal at {current_time.strftime('%Y-%m-%d %H:%M:%S')} (New M15 Candle) ---"
                )

                # جلب البيانات التاريخية (عدد كافٍ من الشموع)
                rates = get_historical_data(
                    SYMBOL, TIMEFRAME_TO_USE, NUM_CANDLES_FOR_DATA
                )

                if rates is None or rates.empty:
                    print(
                        "Failed to get historical data. Skipping signal check this cycle."
                    )
                    time.sleep(60)  # انتظر دقيقة قبل المحاولة مرة أخرى
                    continue

                # حساب مستويات الدعم والمقاومة التلقائية
                sr_levels = calculate_dynamic_support_resistance(SYMBOL, rates)
                current_resistance = sr_levels['resistance_level']
                current_support = sr_levels['support_level']

                print(f"🎯 مستويات محدثة: مقاومة={current_resistance}, دعم={current_support}")
                print(f"📊 طريقة الحساب: {sr_levels['method']}")

                # التحقق من إشارة التداول (بما في ذلك الأخبار و MA و S/R)
                # الوظيفة check_for_trade_signal ستعيد العدادات المحدثة
                trade_signal, resistance_touch_count, support_touch_count = (
                    check_for_trade_signal(
                        rates,
                        current_time,
                        resistance_touch_count,  # تمرير العدادات الحالية
                        support_touch_count,  # تمرير العدادات الحالية
                        point_value,
                        current_resistance,  # مستوى المقاومة المحسوب
                        current_support,     # مستوى الدعم المحسوب
                    )
                )

                print(f"Calculated Trade Signal: {trade_signal}")
                print(
                    f"Resistance Touch Count: {resistance_touch_count}, Support Touch Count: {support_touch_count}"
                )

                # التحقق لمنع تكرار التداول في نفس الشمعة
                # (لأننا نتحقق على رأس كل شمعة M15)
                if last_trade_minute != current_time.minute:
                    # إدارة الصفقات بناءً على الإشارة
                    # manage_positions ستعيد نوع المركز المفتوح الحالي
                    current_open_position_type = manage_positions(
                        SYMBOL, trade_signal, trade_volume, current_open_position_type
                    )
                    last_trade_minute = current_time.minute  # تحديث الدقيقة لآخر تداول
                else:
                    print(
                        f"Trade already processed for minute {current_time.minute}. Skipping."
                    )

                # انتظر حتى بداية الشمعة M15 التالية
                minutes_past_quarter = current_time.minute % 15
                seconds_to_next_quarter = (
                    15 - minutes_past_quarter
                ) * 60 - current_time.second

                sleep_duration = max(
                    10, seconds_to_next_quarter
                )  # لا تنام أقل من 10 ثواني
                print(
                    f"Sleeping for {sleep_duration} seconds until next M15 candle check..."
                )
                time.sleep(sleep_duration)

            else:
                # إذا لم تكن بداية شمعة M15، انتظر 10 ثوانٍ ثم تحقق مرة أخرى
                print(
                    f"Waiting for M15 candle start. Current time: {current_time.strftime('%H:%M:%S')}"
                )
                time.sleep(10)

    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        time.sleep(60)  # انتظر دقيقة قبل المحاولة مرة أخرى في حالة الخطأ
    finally:
        # إغلاق الاتصال عند إيقاف السكريبت يدوياً (Ctrl+C)
        shutdown_mt5_connection()
