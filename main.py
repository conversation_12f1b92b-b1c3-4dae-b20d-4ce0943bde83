# main.py

import MetaTrader5 as mt5
import pandas as pd
import time

# استيراد الوحدات والوظائف من الملفات الأخرى
try:
    # محاولة الاستيراد النسبي (عند التشغيل كجزء من حزمة)
    from .config import (
        SYMBOL,
        TIMEFRAME_TO_USE,
        NUM_CANDLES_FOR_DATA,
        TOUCH_TOLERANCE_POINTS,
        BREAKOUT_CONFIRMATION_POINTS,
    )
    from .mt5_interface import (
        initialize_mt5_connection,
        shutdown_mt5_connection,
        get_account_details,
        get_historical_data,
    )
    from .strategy_logic import check_for_trade_signal, manage_positions
    from .support_resistance import calculate_dynamic_support_resistance
except ImportError:
    # الاستيراد المباشر (عند التشغيل كملف منفصل)
    from config import (
        SYMBOL,
        TIMEFRAME_TO_USE,
        NUM_CANDLES_FOR_DATA,
        TOUCH_TOLERANCE_POINTS,
        BREAKOUT_CONFIRMATION_POINTS,
    )
    from mt5_interface import (
        initialize_mt5_connection,
        shutdown_mt5_connection,
        get_account_details,
        get_historical_data,
    )
    from strategy_logic import check_for_trade_signal, manage_positions
    from support_resistance import calculate_dynamic_support_resistance

# --- المتغيرات الجلوبال للحفاظ على حالة البوت ---
# هذه المتغيرات ستحفظ حالتها بين دورات الحلقة الرئيسية
# سنقوم بتحديثها بناءً على المخرجات من وظائف strategy_logic
resistance_touch_count = 0
support_touch_count = 0
current_open_position_type = None  # 'BUY', 'SELL', أو None
last_trade_minute = -1  # لتخزين الدقيقة التي تم فيها آخر تداول (لمنع التكرار)

# --- الجزء الرئيسي لتشغيل السكريبت ---
if __name__ == "__main__":
    if not initialize_mt5_connection():
        exit()  # إذا فشل الاتصال، نخرج من السكريبت

    try:
        account_data = get_account_details()
        if not account_data or account_data.balance <= 0:
            print("Account balance is zero or not available. Cannot place trades.")
            # يمكنك اختيار exit() هنا إذا كنت لا تريد تشغيل البوت بدون رصيد
            # ولكن سنستمر للسماح بعرض البيانات حتى لو لم يكن هناك رصيد كافٍ للتداول

        # الحصول على معلومات الرمز لتحديد حجم اللوت الأدنى وقيمة النقطة
        symbol_info = mt5.symbol_info(SYMBOL)
        if not symbol_info:
            print(f"Failed to get symbol info for {SYMBOL}. Exiting.")
            shutdown_mt5_connection()
            exit()

        trade_volume = symbol_info.volume_min  # حجم التداول الأدنى المسموح به
        point_value = symbol_info.point  # قيمة النقطة للرمز

        # حساب هوامش الملامسة والكسر بناءً على قيمة النقطة
        TOUCH_TOLERANCE_VALUE = TOUCH_TOLERANCE_POINTS * point_value
        BREAKOUT_CONFIRMATION_VALUE = BREAKOUT_CONFIRMATION_POINTS * point_value

        print(
            f"\n--- Starting {str(TIMEFRAME_TO_USE).split('_')[-1]} Trading Loop for {SYMBOL} ---"
        )
        print(f"Minimum allowed volume for {SYMBOL}: {trade_volume}")
        print(f"Point value for {SYMBOL}: {point_value}")
        print(
            f"Touch Tolerance: {TOUCH_TOLERANCE_VALUE:.5f}, Breakout Confirmation Margin: {BREAKOUT_CONFIRMATION_VALUE:.5f}"
        )

        # حلقة لا نهائية لتشغيل البوت
        while True:
            current_time = pd.to_datetime(mt5.time_srv(), unit="s")

            # منطق المزامنة مع الشمعة (لإطار M15)
            # ننتظر حتى مرور بضع ثوانٍ من بداية ربع الساعة (0, 15, 30, 45)
            # للتأكد من أن الشمعة السابقة قد أغلقت بالكامل
            if current_time.minute % 15 == 0 and current_time.second < 10:
                print(
                    f"\n--- Checking signal at {current_time.strftime('%Y-%m-%d %H:%M:%S')} (New M15 Candle) ---"
                )

                # جلب البيانات التاريخية (عدد كافٍ من الشموع)
                rates = get_historical_data(
                    SYMBOL, TIMEFRAME_TO_USE, NUM_CANDLES_FOR_DATA
                )

                if rates is None or rates.empty:
                    print(
                        "Failed to get historical data. Skipping signal check this cycle."
                    )
                    time.sleep(60)  # انتظر دقيقة قبل المحاولة مرة أخرى
                    continue

                # Calculate automatic support and resistance levels
                sr_levels = calculate_dynamic_support_resistance(SYMBOL, rates)
                current_resistance = sr_levels['resistance_level']
                current_support = sr_levels['support_level']

                print(f"🎯 Updated levels: resistance={current_resistance}, support={current_support}")
                print(f"📊 Calculation method: {sr_levels['method']}")

                # Check for trade signal (including news, MA, and S/R)
                # The check_for_trade_signal function will return updated counters
                trade_signal, resistance_touch_count, support_touch_count = (
                    check_for_trade_signal(
                        rates,
                        current_time,
                        resistance_touch_count,  # Pass current counters
                        support_touch_count,  # Pass current counters
                        point_value,
                        current_resistance,  # Calculated resistance level
                        current_support,     # Calculated support level
                    )
                )

                print(f"Calculated Trade Signal: {trade_signal}")
                print(
                    f"Resistance Touch Count: {resistance_touch_count}, Support Touch Count: {support_touch_count}"
                )

                # Check to prevent duplicate trading in same candle
                # (since we check at the start of each M15 candle)
                if last_trade_minute != current_time.minute:
                    # Manage trades based on signal
                    # manage_positions will return current open position type
                    current_open_position_type = manage_positions(
                        SYMBOL, trade_signal, trade_volume, current_open_position_type
                    )
                    last_trade_minute = current_time.minute  # Update minute for last trade
                else:
                    print(
                        f"Trade already processed for minute {current_time.minute}. Skipping."
                    )

                # Wait until next M15 candle start
                minutes_past_quarter = current_time.minute % 15
                seconds_to_next_quarter = (
                    15 - minutes_past_quarter
                ) * 60 - current_time.second

                sleep_duration = max(
                    10, seconds_to_next_quarter
                )  # Don't sleep less than 10 seconds
                print(
                    f"Sleeping for {sleep_duration} seconds until next M15 candle check..."
                )
                time.sleep(sleep_duration)

            else:
                # If not M15 candle start, wait 10 seconds then check again
                print(
                    f"Waiting for M15 candle start. Current time: {current_time.strftime('%H:%M:%S')}"
                )
                time.sleep(10)

    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        time.sleep(60)  # Wait a minute before trying again in case of error
    finally:
        # Close connection when script is stopped manually (Ctrl+C)
        shutdown_mt5_connection()
