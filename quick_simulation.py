# quick_simulation.py
# اختبار سريع للمحاكاة

from datetime import datetime, timedelta
from simulation import TradingSimulation


def quick_test():
    """
    اختبار سريع على آخر 3 أيام
    """
    print("⚡ اختبار سريع - آخر 3 أيام")
    print("="*50)
    
    # تحديد الفترة
    end_date = datetime.now()
    start_date = end_date - timedelta(days=3)
    
    print(f"📅 الفترة: {start_date.strftime('%Y-%m-%d')} إلى {end_date.strftime('%Y-%m-%d')}")
    
    # إنشاء المحاكي
    sim = TradingSimulation(
        initial_balance=1000,   # 1,000$ للاختبار السريع
        lot_size=0.01,          # 0.01 لوت
        spread=0.5              # 0.5 نقطة سبريد
    )
    
    # تشغيل المحاكاة
    success = sim.run_simulation(start_date, end_date, candles_per_step=50)
    
    if success:
        results = sim.print_results()
        
        # تحليل سريع
        print("\n🔍 تحليل سريع:")
        if results['total_trades'] > 0:
            if results['profit_percentage'] > 0:
                print("✅ النتيجة إيجابية - البوت يحقق أرباح")
            else:
                print("⚠️ النتيجة سلبية - يحتاج تحسين")
            
            if results['win_rate'] > 50:
                print(f"✅ معدل نجاح جيد: {results['win_rate']:.1f}%")
            else:
                print(f"⚠️ معدل نجاح منخفض: {results['win_rate']:.1f}%")
        else:
            print("ℹ️ لم يتم تنفيذ أي صفقات في هذه الفترة")
        
        return results
    else:
        print("❌ فشل الاختبار")
        return None


def performance_test():
    """
    اختبار الأداء - قياس سرعة المحاكاة
    """
    print("⏱️ اختبار الأداء")
    print("="*50)
    
    start_time = datetime.now()
    
    # تشغيل اختبار سريع
    results = quick_test()
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    print(f"\n⏱️ وقت التنفيذ: {duration:.2f} ثانية")
    
    if duration < 30:
        print("✅ أداء ممتاز - سريع جداً")
    elif duration < 60:
        print("✅ أداء جيد")
    else:
        print("⚠️ أداء بطيء - قد تحتاج لتحسين")
    
    return results, duration


if __name__ == "__main__":
    print("🚀 بدء الاختبار السريع...")
    
    # تشغيل اختبار الأداء
    results, duration = performance_test()
    
    if results:
        print(f"\n🎯 ملخص سريع:")
        print(f"   💰 الربح: {results['total_profit']:+.2f}$")
        print(f"   📊 الصفقات: {results['total_trades']}")
        print(f"   ⏱️ الوقت: {duration:.1f}s")
        
        if results['total_trades'] > 0:
            print(f"   🎯 النجاح: {results['win_rate']:.1f}%")
    
    print("\n✅ انتهى الاختبار السريع!")
    print("💡 لاختبارات أكثر تفصيلاً، استخدم: python run_simulation.py")
