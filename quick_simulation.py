# quick_simulation.py
# اختبار سريع للمحاكاة

from datetime import datetime, timedelta
from simulation import TradingSimulation


def quick_test():
    """
    Quick test on last 3 days
    """
    print("⚡ Quick test - last 3 days")
    print("="*50)

    # Define period
    end_date = datetime.now()
    start_date = end_date - timedelta(days=3)

    print(f"📅 Period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")

    # Create simulator
    sim = TradingSimulation(
        initial_balance=1000,   # $1,000 for quick test
        lot_size=0.01,          # 0.01 lot
        spread=0.5              # 0.5 point spread
    )
    
    # Run simulation
    success = sim.run_simulation(start_date, end_date, candles_per_step=50)

    if success:
        results = sim.print_results()

        # Quick analysis
        print("\n🔍 Quick analysis:")
        if results['total_trades'] > 0:
            if results['profit_percentage'] > 0:
                print("✅ Positive result - bot is profitable")
            else:
                print("⚠️ Negative result - needs improvement")

            if results['win_rate'] > 50:
                print(f"✅ Good win rate: {results['win_rate']:.1f}%")
            else:
                print(f"⚠️ Low win rate: {results['win_rate']:.1f}%")
        else:
            print("ℹ️ No trades executed in this period")

        return results
    else:
        print("❌ Test failed")
        return None


def performance_test():
    """
    Performance test - measure simulation speed
    """
    print("⏱️ Performance test")
    print("="*50)

    start_time = datetime.now()

    # Run quick test
    results = quick_test()

    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()

    print(f"\n⏱️ Execution time: {duration:.2f} seconds")

    if duration < 30:
        print("✅ Excellent performance - very fast")
    elif duration < 60:
        print("✅ Good performance")
    else:
        print("⚠️ Slow performance - may need optimization")

    return results, duration


if __name__ == "__main__":
    print("🚀 Starting quick test...")

    # Run performance test
    results, duration = performance_test()

    if results:
        print(f"\n🎯 Quick summary:")
        print(f"   💰 Profit: {results['total_profit']:+.2f}$")
        print(f"   📊 Trades: {results['total_trades']}")
        print(f"   ⏱️ Time: {duration:.1f}s")

        if results['total_trades'] > 0:
            print(f"   🎯 Success: {results['win_rate']:.1f}%")

    print("\n✅ Quick test completed!")
    print("💡 For more detailed tests, use: python run_simulation.py")
