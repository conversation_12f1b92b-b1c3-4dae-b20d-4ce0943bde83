#!/usr/bin/env python3
# test_optimized_signals.py
# Optimized individual signal triggers with better filtering

import sys
from datetime import datetime, timedelta
from simulation import AdvancedTradingSimulation

def test_optimized_individual_signals():
    """
    Test optimized individual signals system with better filtering
    """
    print("🔥 Testing OPTIMIZED Individual Signal Triggers")
    print("=" * 60)
    print("📋 Optimizations applied:")
    print("   • Higher confidence thresholds (70%+ for entry)")
    print("   • Anti-conflict rules (no opposite signals)")
    print("   • Quality over quantity approach")
    print("   • Better risk management")
    print("=" * 60)
    
    # Create simulation instance with optimized settings
    sim = AdvancedTradingSimulation(
        initial_balance=10000,
        base_lot_size=0.01,
        spread=0.5,
        use_stop_loss=True
    )
    
    # Optimized parameters
    sim.max_positions = 3  # Fewer but better positions
    sim.stop_loss_percentage = 0.005  # Wider stop loss for better survival
    
    # Test period (last 3 days)
    end_date = datetime.now()
    start_date = end_date - timedelta(days=3)
    
    print(f"📅 Test period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    print(f"🎯 Max positions: {sim.max_positions}")
    print(f"🛡️ Stop loss: {sim.stop_loss_percentage*100:.1f}%")
    print(f"📊 Min confidence: 70%")
    print()
    
    # Override the simulation method with optimized logic
    def run_optimized_simulation(self, start_date, end_date, candles_per_step=200):
        """
        Optimized simulation with better signal filtering
        """
        print(f"🚀 Starting Optimized Individual Signals Simulation")
        print("-" * 60)
        
        if not self.initialize_mt5_connection():
            print("❌ Failed to connect to MT5")
            return False
        
        try:
            from mt5_interface import get_historical_data
            from support_resistance import calculate_dynamic_support_resistance
            from technical_indicators import get_all_indicators, analyze_trend_strength, get_individual_signals
            from strategy import get_risk_management_params
            from config import SYMBOL, TIMEFRAME_TO_USE
            import pandas as pd
            
            # Get historical data
            total_candles = 2000
            rates = get_historical_data(SYMBOL, TIMEFRAME_TO_USE, total_candles)
            
            if rates is None or rates.empty:
                print("❌ Failed to get historical data")
                return False
            
            print(f"📊 Retrieved {len(rates)} candles")
            
            # Filter data by date
            rates['time'] = pd.to_datetime(rates['time'])
            mask = (rates['time'] >= start_date) & (rates['time'] <= end_date)
            simulation_data = rates.loc[mask].copy()
            
            if simulation_data.empty:
                print("❌ No data in specified period")
                return False
            
            print(f"📈 Simulating on {len(simulation_data)} candles")
            print("-" * 60)
            
            # Run optimized simulation
            for i in range(candles_per_step, len(simulation_data)):
                current_candle = simulation_data.iloc[i]
                current_time = current_candle['time']
                current_price = current_candle['close']
                
                # Get analysis data
                analysis_data = simulation_data.iloc[i-candles_per_step:i].copy()
                
                # Calculate S/R levels
                sr_levels = calculate_dynamic_support_resistance(SYMBOL, analysis_data)
                resistance_level = sr_levels['resistance_level']
                support_level = sr_levels['support_level']
                
                # Get indicators and trend
                indicators = get_all_indicators(analysis_data)
                if indicators:
                    trend = analyze_trend_strength(indicators)
                    self.trend_analysis.append(trend)
                    
                    # Check stop loss for all open positions
                    positions_to_close = []
                    for pos in self.open_positions:
                        if self.use_stop_loss and pos['stop_loss']:
                            if ((pos['type'] == "BUY" and current_price <= pos['stop_loss']) or
                                (pos['type'] == "SELL" and current_price >= pos['stop_loss'])):
                                positions_to_close.append(pos['id'])
                    
                    # Close positions that hit stop loss
                    for pos_id in positions_to_close:
                        self.close_individual_trade(pos_id, current_price, current_time, "Stop Loss Hit")
                    
                    # Get individual signals
                    individual_signals = get_individual_signals(
                        indicators, current_price, support_level, resistance_level, trend
                    )
                    
                    # OPTIMIZED SIGNAL FILTERING
                    high_quality_signals = []
                    for rule_name, signal, confidence in individual_signals:
                        # Higher confidence threshold
                        if confidence >= 70:
                            # Anti-conflict filtering
                            conflicting_positions = [pos for pos in self.open_positions 
                                                   if ((pos['type'] == "BUY" and signal == "SELL") or 
                                                       (pos['type'] == "SELL" and signal == "BUY"))]
                            
                            # Only proceed if no conflicting positions or very high confidence
                            if not conflicting_positions or confidence >= 85:
                                high_quality_signals.append((rule_name, signal, confidence))
                    
                    # Execute only the best signals
                    high_quality_signals.sort(key=lambda x: x[2], reverse=True)  # Sort by confidence
                    
                    for rule_name, signal, confidence in high_quality_signals[:2]:  # Max 2 signals per candle
                        # Check if we already have a position from this rule
                        existing_rule_positions = [pos for pos in self.open_positions if pos.get('rule_name') == rule_name]
                        
                        if not existing_rule_positions:  # Only open if no existing position from this rule
                            volume = get_risk_management_params(confidence, self.base_lot_size)
                            
                            success = self.open_individual_trade(
                                signal, current_price, current_time, confidence, volume, rule_name
                            )
                            if success:
                                print(f"🎯 HIGH QUALITY {signal} by {rule_name} (Conf: {confidence:.1f}%)")
            
            # Close all remaining positions
            for pos in self.open_positions.copy():
                final_price = simulation_data.iloc[-1]['close']
                final_time = simulation_data.iloc[-1]['time']
                self.close_individual_trade(pos['id'], final_price, final_time, "End of Simulation")
            
            return True
            
        finally:
            from mt5_interface import shutdown_mt5_connection
            shutdown_mt5_connection()
    
    # Bind the optimized method
    import types
    sim.run_optimized_simulation = types.MethodType(run_optimized_simulation, sim)
    
    # Run optimized simulation
    success = sim.run_optimized_simulation(start_date, end_date)
    
    if success:
        print("\n" + "🎉 Optimized Individual Signals Test Completed!")
        print("=" * 60)
        
        # Print enhanced results
        results = sim.print_advanced_results()
        
        # Additional analysis
        print("\n📊 Optimization Results:")
        print("-" * 40)
        
        if sim.trades:
            # Quality metrics
            high_conf_trades = [t for t in sim.trades if t['confidence'] >= 80]
            medium_conf_trades = [t for t in sim.trades if 70 <= t['confidence'] < 80]
            
            print(f"🎯 Quality Distribution:")
            print(f"   • High confidence (80%+): {len(high_conf_trades)} trades")
            print(f"   • Medium confidence (70-80%): {len(medium_conf_trades)} trades")
            
            if high_conf_trades:
                high_conf_win_rate = (len([t for t in high_conf_trades if t['profit'] > 0]) / len(high_conf_trades)) * 100
                print(f"   • High confidence win rate: {high_conf_win_rate:.1f}%")
            
            # Compare with previous test
            print(f"\n📈 Comparison with Previous Test:")
            print(f"   • Previous: 148 trades, 2% win rate, -0.47% profit")
            print(f"   • Optimized: {len(sim.trades)} trades, {results['win_rate']:.1f}% win rate, {results['profit_percentage']:.2f}% profit")
            
            if results['win_rate'] > 10:
                print("✅ SUCCESS: Significantly improved win rate!")
            elif results['profit_percentage'] > 0:
                print("✅ SUCCESS: Achieved profitability!")
            else:
                print("⚠️ PARTIAL: Still needs fine-tuning")
                
            # Activity level assessment
            daily_trades = len(sim.trades) / 3
            if daily_trades >= 10:
                print(f"✅ EXCELLENT: {daily_trades:.1f} trades/day (still very active)")
            elif daily_trades >= 5:
                print(f"✅ GOOD: {daily_trades:.1f} trades/day (balanced activity)")
            else:
                print(f"⚠️ LOW: {daily_trades:.1f} trades/day (may need more signals)")
        
        print("\n🎯 Final Assessment:")
        if results['profit_percentage'] > 0 and results['win_rate'] > 20:
            print("🏆 OPTIMAL: High activity + Good quality + Profitable")
        elif results['profit_percentage'] > 0:
            print("✅ GOOD: Profitable with room for improvement")
        elif len(sim.trades) > 20:
            print("⚡ ACTIVE: Very active but needs quality improvement")
        else:
            print("🔧 NEEDS WORK: Requires further optimization")
            
        return results
    else:
        print("❌ Optimized test failed")
        return None

if __name__ == "__main__":
    test_optimized_individual_signals()
