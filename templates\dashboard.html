<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 Trading Bot Control Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: bold;
        }
        .btn-success {
            background: linear-gradient(45deg, #56ab2f, #a8e6cf);
            border: none;
            border-radius: 25px;
        }
        .btn-danger {
            background: linear-gradient(45deg, #ff416c, #ff4b2b);
            border: none;
            border-radius: 25px;
        }
        .status-running {
            color: #28a745;
            animation: pulse 2s infinite;
        }
        .status-stopped {
            color: #dc3545;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .result-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        .profit-positive { color: #28a745; font-weight: bold; }
        .profit-negative { color: #dc3545; font-weight: bold; }
        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <h1 class="display-4 mb-0">
                            <i class="fas fa-robot text-primary"></i>
                            لوحة تحكم بوت التداول
                        </h1>
                        <p class="lead text-muted">نظام التأكيد المزدوج للتداول الذكي</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Control Panel -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-cogs"></i> إعدادات سريعة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">عدد القواعد المطلوبة</label>
                                <select class="form-select" id="minRules">
                                    <option value="1" {{ 'selected' if config.trading_settings.min_rules_required == 1 }}>قاعدة واحدة</option>
                                    <option value="2" {{ 'selected' if config.trading_settings.min_rules_required == 2 }}>قاعدتان (مستحسن)</option>
                                    <option value="3" {{ 'selected' if config.trading_settings.min_rules_required == 3 }}>ثلاث قواعد</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">مدة الاختبار (أيام)</label>
                                <input type="number" class="form-control" id="testDays" 
                                       value="{{ config.simulation_settings.test_days }}" min="1" max="30">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">حجم الصفقة الأساسي</label>
                                <input type="number" class="form-control" id="baseLotSize" 
                                       value="{{ config.trading_settings.base_lot_size }}" step="0.01" min="0.01">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الحد الأدنى للثقة (%)</label>
                                <input type="number" class="form-control" id="minConfidence" 
                                       value="{{ config.trading_settings.min_confidence }}" min="50" max="95">
                            </div>
                        </div>
                        <div class="text-center">
                            <a href="/settings" class="btn btn-outline-primary">
                                <i class="fas fa-sliders-h"></i> إعدادات متقدمة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-play-circle"></i> تشغيل المحاكاة</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <h6>حالة النظام:</h6>
                            <span id="systemStatus" class="h5 status-stopped">
                                <i class="fas fa-stop-circle"></i> متوقف
                            </span>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button id="runBtn" class="btn btn-success btn-lg" onclick="runSimulation()">
                                <i class="fas fa-rocket"></i> تشغيل المحاكاة
                            </button>
                            <button id="stopBtn" class="btn btn-danger" onclick="stopSimulation()" disabled>
                                <i class="fas fa-stop"></i> إيقاف
                            </button>
                        </div>
                        
                        <div id="progressArea" class="mt-3" style="display: none;">
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 100%"></div>
                            </div>
                            <small class="text-muted">جاري تشغيل المحاكاة...</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <div id="resultsSection" style="display: none;">
            <div class="row">
                <div class="col-12">
                    <div class="card result-card">
                        <div class="card-header">
                            <h5 class="mb-0 text-white">
                                <i class="fas fa-chart-line"></i> نتائج المحاكاة
                            </h5>
                        </div>
                        <div class="card-body bg-white text-dark">
                            <div class="row" id="resultsContent">
                                <!-- Results will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Settings Display -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-info-circle"></i> الإعدادات الحالية</h5>
                    </div>
                    <div class="card-body">
                        <div class="settings-grid">
                            <div>
                                <h6><i class="fas fa-coins"></i> إعدادات التداول</h6>
                                <ul class="list-unstyled">
                                    <li><strong>حجم الصفقة:</strong> {{ config.trading_settings.base_lot_size }}</li>
                                    <li><strong>أقصى صفقات متزامنة:</strong> {{ config.trading_settings.max_positions }}</li>
                                    <li><strong>وقف الخسارة:</strong> {{ (config.trading_settings.stop_loss_percentage * 100)|round(2) }}%</li>
                                    <li><strong>عدد القواعد المطلوبة:</strong> {{ config.trading_settings.min_rules_required }}</li>
                                </ul>
                            </div>
                            <div>
                                <h6><i class="fas fa-chart-bar"></i> إعدادات المحاكاة</h6>
                                <ul class="list-unstyled">
                                    <li><strong>مدة الاختبار:</strong> {{ config.simulation_settings.test_days }} أيام</li>
                                    <li><strong>الرصيد الأولي:</strong> ${{ config.simulation_settings.initial_balance }}</li>
                                    <li><strong>السبريد:</strong> {{ config.simulation_settings.spread }} نقطة</li>
                                </ul>
                            </div>
                            <div>
                                <h6><i class="fas fa-rules"></i> القواعد المفعلة</h6>
                                <small class="text-muted">{{ config.rule_settings.enabled_rules|length }} قاعدة مفعلة</small>
                                <div class="mt-2">
                                    <a href="/settings" class="btn btn-sm btn-outline-info">عرض التفاصيل</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let statusCheckInterval;

        function updateQuickSettings() {
            const settings = {
                trading_settings: {
                    min_rules_required: parseInt(document.getElementById('minRules').value),
                    base_lot_size: parseFloat(document.getElementById('baseLotSize').value),
                    min_confidence: parseInt(document.getElementById('minConfidence').value)
                },
                simulation_settings: {
                    test_days: parseInt(document.getElementById('testDays').value)
                }
            };

            fetch('/api/update_settings', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(settings)
            });
        }

        // Auto-save settings on change
        document.getElementById('minRules').addEventListener('change', updateQuickSettings);
        document.getElementById('testDays').addEventListener('change', updateQuickSettings);
        document.getElementById('baseLotSize').addEventListener('change', updateQuickSettings);
        document.getElementById('minConfidence').addEventListener('change', updateQuickSettings);

        function runSimulation() {
            updateQuickSettings(); // Save current settings first
            
            fetch('/api/run_simulation', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('runBtn').disabled = true;
                        document.getElementById('stopBtn').disabled = false;
                        document.getElementById('progressArea').style.display = 'block';
                        document.getElementById('systemStatus').innerHTML = '<i class="fas fa-spinner fa-spin"></i> يعمل';
                        document.getElementById('systemStatus').className = 'h5 status-running';
                        
                        // Start checking status
                        statusCheckInterval = setInterval(checkStatus, 2000);
                    } else {
                        alert('خطأ: ' + data.error);
                    }
                });
        }

        function stopSimulation() {
            fetch('/api/stop_simulation', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resetUI();
                    }
                });
        }

        function checkStatus() {
            fetch('/api/simulation_status')
                .then(response => response.json())
                .then(data => {
                    if (!data.running) {
                        resetUI();
                        if (data.results) {
                            displayResults(data.results);
                        }
                    }
                });
        }

        function resetUI() {
            document.getElementById('runBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            document.getElementById('progressArea').style.display = 'none';
            document.getElementById('systemStatus').innerHTML = '<i class="fas fa-stop-circle"></i> متوقف';
            document.getElementById('systemStatus').className = 'h5 status-stopped';
            
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
            }
        }

        function displayResults(results) {
            if (!results.success) {
                document.getElementById('resultsContent').innerHTML = 
                    `<div class="col-12"><div class="alert alert-danger">خطأ: ${results.error}</div></div>`;
                document.getElementById('resultsSection').style.display = 'block';
                return;
            }

            const stats = results.detailed_stats;
            const profitClass = stats.balance_change >= 0 ? 'profit-positive' : 'profit-negative';
            const profitIcon = stats.balance_change >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';

            document.getElementById('resultsContent').innerHTML = `
                <div class="col-md-3 mb-3">
                    <div class="text-center">
                        <h6><i class="fas fa-chart-line"></i> إجمالي الصفقات</h6>
                        <h4 class="text-primary">${stats.total_trades}</h4>
                        <small class="text-muted">${stats.daily_trades.toFixed(1)} صفقة/يوم</small>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="text-center">
                        <h6><i class="fas fa-percentage"></i> معدل النجاح</h6>
                        <h4 class="text-info">${((stats.winning_trades / stats.total_trades) * 100).toFixed(1)}%</h4>
                        <small class="text-muted">${stats.winning_trades} رابحة / ${stats.losing_trades} خاسرة</small>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="text-center">
                        <h6><i class="fas ${profitIcon}"></i> الربح/الخسارة</h6>
                        <h4 class="${profitClass}">$${stats.balance_change.toFixed(2)}</h4>
                        <small class="text-muted">${stats.balance_change_pct.toFixed(2)}%</small>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="text-center">
                        <h6><i class="fas fa-trophy"></i> أفضل/أسوأ صفقة</h6>
                        <h4 class="profit-positive">$${stats.best_trade.toFixed(2)}</h4>
                        <h4 class="profit-negative">$${stats.worst_trade.toFixed(2)}</h4>
                    </div>
                </div>
                <div class="col-12 mt-3">
                    <small class="text-muted">
                        <i class="fas fa-clock"></i> تم الانتهاء في: ${results.timestamp}
                    </small>
                </div>
            `;
            
            document.getElementById('resultsSection').style.display = 'block';
        }

        // Check initial status
        checkStatus();
    </script>
</body>
</html>
